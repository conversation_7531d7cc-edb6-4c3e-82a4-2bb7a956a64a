import { Log<PERSON><PERSON><PERSON>, Point, ParseResult } from '@/types';
import { v4 as uuidv4 } from 'uuid';

/**
 * 日志解析器类
 */
export class LogParser {
  private static readonly DELIMITER =
    '------------------------------------------------------------   (MCtrlStage.cpp, 3718)';
  private static readonly STU_PATTERN = /STU([12])/;
  private static readonly COORDINATE_PATTERN = /X([\d.-]+)\s+YY?([\d.-]+)/;
  private static readonly RADIUS_PATTERN = /R([\d.-]+)/;
  private static readonly VALUES_PATTERN =
    /m1\s+([\d.-]+),\s*m2\s+([\d.-]+),\s*m3\s+([\d.-]+),\s*tanValue1\s+([\d.-]+),\s*tanValue2\s+([\d.-]+),\s*tanSpec\s+([\d.-]+)/;

  /**
   * 解析日志文件内容
   * @param content 日志文件内容
   * @returns 解析结果
   */
  static parseLogContent(content: string): ParseResult {
    const lines = content
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);
    const records: LogRecord[] = [];
    const errors: string[] = [];

    let currentBlock: string[] = [];
    let blockStartLine = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      if (line.includes(this.DELIMITER)) {
        // 处理当前块
        if (currentBlock.length > 0) {
          try {
            const record = this.parseBlock(currentBlock, blockStartLine);
            if (record) {
              records.push(record);
            }
          } catch (error) {
            errors.push(
              `第 ${blockStartLine + 1} 行开始的数据块解析失败: ${error instanceof Error ? error.message : '未知错误'}`
            );
          }
        }

        // 重置块
        currentBlock = [];
        blockStartLine = i + 1;
      } else {
        currentBlock.push(line);
      }
    }

    // 处理最后一个块（如果存在）
    if (currentBlock.length > 0) {
      try {
        const record = this.parseBlock(currentBlock, blockStartLine);
        if (record) {
          records.push(record);
        }
      } catch (error) {
        errors.push(
          `第 ${blockStartLine + 1} 行开始的数据块解析失败: ${error instanceof Error ? error.message : '未知错误'}`
        );
      }
    }

    return {
      success: errors.length === 0,
      records,
      errors,
      totalLines: lines.length,
      parsedRecords: records.length,
    };
  }

  /**
   * 解析单个数据块
   * @param block 数据块行数组
   * @param startLine 起始行号
   * @returns 解析的记录
   */
  private static parseBlock(
    block: string[],
    startLine: number
  ): LogRecord | null {
    if (block.length < 8) {
      throw new Error(`数据块行数不足，期望8行，实际${block.length}行`);
    }

    // 提取时间戳
    const timestampMatch = block[0].match(/\[([\d-\s:.]+)\]/);
    const timestamp = timestampMatch
      ? timestampMatch[1]
      : new Date().toISOString();

    // 第1行：提取机台号
    const stuMatch = block[0].match(this.STU_PATTERN);
    if (!stuMatch) {
      throw new Error('无法提取机台号');
    }
    const stageNum = `STU${stuMatch[1]}` as 'STU1' | 'STU2';

    // 第2行：D1坐标点
    const d1 = this.extractCoordinates(block[1], '第2行D1坐标');

    // 第3行：D2坐标点
    const d2 = this.extractCoordinates(block[2], '第3行D2坐标');

    // 第4行：D3坐标点 + Ra半径值
    const d3 = this.extractCoordinates(block[3], '第4行D3坐标');
    const ra = this.extractRadius(block[3], '第4行Ra半径');

    // 第5行：D4坐标点
    const d4 = this.extractCoordinates(block[4], '第5行D4坐标');

    // 第6行：D5坐标点 + Rb半径值
    const d5 = this.extractCoordinates(block[5], '第6行D5坐标');
    const rb = this.extractRadius(block[5], '第6行Rb半径');

    // 第7行：D6坐标点
    const d6 = this.extractCoordinates(block[6], '第7行D6坐标');

    // 第8行：计算参数
    const valuesMatch = block[7].match(this.VALUES_PATTERN);
    if (!valuesMatch) {
      throw new Error('无法提取计算参数');
    }

    const [, m1Str, m2Str, m3Str, tanValue1Str, tanValue2Str, tanSpecStr] =
      valuesMatch;

    return {
      id: uuidv4(),
      timestamp,
      stageNum,
      d1,
      d2,
      d3,
      ra,
      d4,
      d5,
      rb,
      d6,
      m1: parseFloat(m1Str),
      m2: parseFloat(m2Str),
      m3: parseFloat(m3Str),
      tanValue1: parseFloat(tanValue1Str),
      tanValue2: parseFloat(tanValue2Str),
      tanSpec: parseFloat(tanSpecStr),
    };
  }

  /**
   * 提取坐标点
   * @param line 行内容
   * @param context 上下文信息
   * @returns 坐标点
   */
  private static extractCoordinates(line: string, context: string): Point {
    const match = line.match(this.COORDINATE_PATTERN);
    if (!match) {
      throw new Error(`${context}: 无法提取坐标点`);
    }

    const [, xStr, yStr] = match;
    return {
      x: parseFloat(xStr),
      y: parseFloat(yStr),
    };
  }

  /**
   * 提取半径值
   * @param line 行内容
   * @param context 上下文信息
   * @returns 半径值
   */
  private static extractRadius(line: string, context: string): number {
    const match = line.match(this.RADIUS_PATTERN);
    if (!match) {
      throw new Error(`${context}: 无法提取半径值`);
    }

    return parseFloat(match[1]);
  }

  /**
   * 验证解析结果
   * @param records 记录数组
   * @returns 验证结果
   */
  static validateRecords(records: LogRecord[]): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    records.forEach((record, index) => {
      // 检查必要字段
      if (!record.stageNum || !['STU1', 'STU2'].includes(record.stageNum)) {
        errors.push(`记录 ${index + 1}: 机台号无效`);
      }

      // 检查坐标点
      const points = [
        record.d1,
        record.d2,
        record.d3,
        record.d4,
        record.d5,
        record.d6,
      ];
      points.forEach((point, pointIndex) => {
        if (
          typeof point.x !== 'number' ||
          typeof point.y !== 'number' ||
          isNaN(point.x) ||
          isNaN(point.y)
        ) {
          errors.push(`记录 ${index + 1}: D${pointIndex + 1}坐标点无效`);
        }
      });

      // 检查数值字段
      const numericFields = [
        'ra',
        'rb',
        'm1',
        'm2',
        'm3',
        'tanValue1',
        'tanValue2',
        'tanSpec',
      ];
      numericFields.forEach(field => {
        const value = record[field as keyof LogRecord] as number;
        if (typeof value !== 'number' || isNaN(value)) {
          errors.push(`记录 ${index + 1}: ${field}字段无效`);
        }
      });
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
