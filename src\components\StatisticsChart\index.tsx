import React, { useMemo, useState } from 'react';
import { Card, Row, Col, Tabs, Empty, Statistic, Select, Space } from 'antd';
import { Line, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { useAppStore } from '@/stores/useAppStore';
import { MathUtils } from '@/utils/mathUtils';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const { TabPane } = Tabs;
const { Option } = Select;

interface StatisticsChartProps {
  className?: string;
}

const StatisticsChart: React.FC<StatisticsChartProps> = ({ className }) => {
  const { statistics, records } = useAppStore();
  const [selectedChart, setSelectedChart] = useState<string>('stu1-tanValue1');

  // 计算统计数据
  const statisticsData = useMemo(() => {
    if (!statistics || !records.length) return null;

    const calculations = {
      stu1TanValue1: MathUtils.calculateNormalDistribution(
        statistics.stu1TanValue1
      ),
      stu1TanValue2: MathUtils.calculateNormalDistribution(
        statistics.stu1TanValue2
      ),
      stu2TanValue1: MathUtils.calculateNormalDistribution(
        statistics.stu2TanValue1
      ),
      stu2TanValue2: MathUtils.calculateNormalDistribution(
        statistics.stu2TanValue2
      ),
      areas: MathUtils.calculateNormalDistribution(statistics.areas),
    };

    return calculations;
  }, [statistics, records]);

  // 生成正态分布图表数据
  const generateNormalDistributionChart = (
    data: number[],
    mean: number,
    stdDev: number,
    title: string,
    color: string
  ) => {
    if (data.length === 0) return null;

    // 生成直方图数据
    const histogram = MathUtils.createHistogram(data, 20);

    // 生成正态分布曲线
    const normalCurve = MathUtils.generateNormalCurve(mean, stdDev, 100);

    // 计算最大频率用于缩放正态分布曲线
    const maxCount = Math.max(...histogram.bins.map(bin => bin.count));
    const scaleFactor =
      maxCount / Math.max(...normalCurve.map(point => point.y));

    return {
      data: {
        labels: histogram.bins.map(bin => MathUtils.formatNumber(bin.center)),
        datasets: [
          {
            type: 'bar' as const,
            label: '频率分布',
            data: histogram.bins.map(bin => bin.count),
            backgroundColor: `${color}40`,
            borderColor: color,
            borderWidth: 1,
            yAxisID: 'y',
          },
          {
            type: 'line' as const,
            label: '正态分布曲线',
            data: normalCurve.map(point => point.y * scaleFactor),
            borderColor: '#ef4444',
            backgroundColor: 'transparent',
            borderWidth: 2,
            pointRadius: 0,
            yAxisID: 'y',
            tension: 0.4,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: title,
            font: { size: 16, weight: 'bold' as const },
          },
          legend: {
            position: 'top' as const,
          },
          tooltip: {
            callbacks: {
              label: (context: any) => {
                if (context.datasetIndex === 0) {
                  return `频率: ${context.parsed.y}`;
                } else {
                  return `正态分布密度: ${MathUtils.formatNumber(context.parsed.y / scaleFactor, 4)}`;
                }
              },
            },
          },
        },
        scales: {
          x: {
            title: {
              display: true,
              text: '数值',
              font: { size: 12, weight: 'bold' as const },
            },
          },
          y: {
            type: 'linear' as const,
            display: true,
            position: 'left' as const,
            title: {
              display: true,
              text: '频率',
              font: { size: 12, weight: 'bold' as const },
            },
          },
        },
      },
    };
  };

  // 图表配置映射
  const chartConfigs = useMemo(() => {
    if (!statisticsData) return {};

    return {
      'stu1-tanValue1': generateNormalDistributionChart(
        statisticsData.stu1TanValue1.data,
        statisticsData.stu1TanValue1.mean,
        statisticsData.stu1TanValue1.stdDev,
        'STU1机台 - tanValue1 正态分布',
        '#3b82f6'
      ),
      'stu1-tanValue2': generateNormalDistributionChart(
        statisticsData.stu1TanValue2.data,
        statisticsData.stu1TanValue2.mean,
        statisticsData.stu1TanValue2.stdDev,
        'STU1机台 - tanValue2 正态分布',
        '#10b981'
      ),
      'stu2-tanValue1': generateNormalDistributionChart(
        statisticsData.stu2TanValue1.data,
        statisticsData.stu2TanValue1.mean,
        statisticsData.stu2TanValue1.stdDev,
        'STU2机台 - tanValue1 正态分布',
        '#8b5cf6'
      ),
      'stu2-tanValue2': generateNormalDistributionChart(
        statisticsData.stu2TanValue2.data,
        statisticsData.stu2TanValue2.mean,
        statisticsData.stu2TanValue2.stdDev,
        'STU2机台 - tanValue2 正态分布',
        '#f59e0b'
      ),
      areas: generateNormalDistributionChart(
        statisticsData.areas.data,
        statisticsData.areas.mean,
        statisticsData.areas.stdDev,
        '封闭图形面积 正态分布',
        '#ef4444'
      ),
    };
  }, [statisticsData]);

  if (!statistics || !records.length) {
    return (
      <Card title="统计分析" className={`fade-in ${className || ''}`}>
        <Empty
          description="暂无数据，请先上传并解析日志文件"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  if (!statisticsData) {
    return (
      <Card title="统计分析" className={`fade-in ${className || ''}`}>
        <Empty description="无法生成统计数据" />
      </Card>
    );
  }

  const currentChart = chartConfigs[selectedChart];

  return (
    <Card
      title="统计分析"
      className={`fade-in ${className || ''}`}
      extra={
        <Space>
          <span>选择图表:</span>
          <Select
            value={selectedChart}
            style={{ width: 200 }}
            onChange={setSelectedChart}
          >
            <Option value="stu1-tanValue1">STU1 - tanValue1</Option>
            <Option value="stu1-tanValue2">STU1 - tanValue2</Option>
            <Option value="stu2-tanValue1">STU2 - tanValue1</Option>
            <Option value="stu2-tanValue2">STU2 - tanValue2</Option>
            <Option value="areas">封闭图形面积</Option>
          </Select>
        </Space>
      }
    >
      <Row gutter={[16, 16]}>
        {/* 统计概览 */}
        <Col span={24}>
          <Row gutter={16}>
            <Col span={4}>
              <Statistic
                title="STU1记录数"
                value={statistics.stu1TanValue1.length}
                valueStyle={{ color: '#3f8600' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="STU2记录数"
                value={statistics.stu2TanValue1.length}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="总记录数"
                value={records.length}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="平均面积"
                value={statisticsData.areas.mean}
                suffix="mm²"
                precision={2}
                valueStyle={{ color: '#eb2f96' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="面积标准差"
                value={statisticsData.areas.stdDev}
                suffix="mm²"
                precision={2}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Col>
            <Col span={4}>
              <Statistic
                title="面积变异系数"
                value={
                  (statisticsData.areas.stdDev / statisticsData.areas.mean) *
                  100
                }
                suffix="%"
                precision={2}
                valueStyle={{ color: '#52c41a' }}
              />
            </Col>
          </Row>
        </Col>

        {/* 图表显示 */}
        <Col span={24}>
          {currentChart ? (
            <div style={{ height: '400px' }}>
              <Line data={currentChart.data} options={currentChart.options} />
            </div>
          ) : (
            <Empty description="无法生成图表数据" />
          )}
        </Col>

        {/* 详细统计信息 */}
        <Col span={24}>
          <Tabs defaultActiveKey="summary">
            <TabPane tab="统计摘要" key="summary">
              <Row gutter={[16, 16]}>
                {Object.entries(statisticsData).map(([key, dist]) => {
                  const titles = {
                    stu1TanValue1: 'STU1 - tanValue1',
                    stu1TanValue2: 'STU1 - tanValue2',
                    stu2TanValue1: 'STU2 - tanValue1',
                    stu2TanValue2: 'STU2 - tanValue2',
                    areas: '封闭图形面积',
                  };

                  return (
                    <Col span={8} key={key}>
                      <Card
                        size="small"
                        title={titles[key as keyof typeof titles]}
                      >
                        <Row gutter={8}>
                          <Col span={12}>
                            <Statistic
                              title="均值"
                              value={dist.mean}
                              precision={2}
                              valueStyle={{ fontSize: '14px' }}
                            />
                          </Col>
                          <Col span={12}>
                            <Statistic
                              title="标准差"
                              value={dist.stdDev}
                              precision={2}
                              valueStyle={{ fontSize: '14px' }}
                            />
                          </Col>
                          <Col span={12}>
                            <Statistic
                              title="最小值"
                              value={Math.min(...dist.data)}
                              precision={2}
                              valueStyle={{ fontSize: '14px' }}
                            />
                          </Col>
                          <Col span={12}>
                            <Statistic
                              title="最大值"
                              value={Math.max(...dist.data)}
                              precision={2}
                              valueStyle={{ fontSize: '14px' }}
                            />
                          </Col>
                        </Row>
                      </Card>
                    </Col>
                  );
                })}
              </Row>
            </TabPane>
          </Tabs>
        </Col>
      </Row>
    </Card>
  );
};

export default StatisticsChart;
