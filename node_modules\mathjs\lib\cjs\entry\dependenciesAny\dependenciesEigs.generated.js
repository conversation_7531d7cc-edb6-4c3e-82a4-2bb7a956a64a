"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.eigsDependencies = void 0;
var _dependenciesAbsGenerated = require("./dependenciesAbs.generated.js");
var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");
var _dependenciesAddScalarGenerated = require("./dependenciesAddScalar.generated.js");
var _dependenciesAtanGenerated = require("./dependenciesAtan.generated.js");
var _dependenciesBignumberGenerated = require("./dependenciesBignumber.generated.js");
var _dependenciesColumnGenerated = require("./dependenciesColumn.generated.js");
var _dependenciesComplexGenerated = require("./dependenciesComplex.generated.js");
var _dependenciesCosGenerated = require("./dependenciesCos.generated.js");
var _dependenciesDiagGenerated = require("./dependenciesDiag.generated.js");
var _dependenciesDivideScalarGenerated = require("./dependenciesDivideScalar.generated.js");
var _dependenciesDotGenerated = require("./dependenciesDot.generated.js");
var _dependenciesEqualGenerated = require("./dependenciesEqual.generated.js");
var _dependenciesFlattenGenerated = require("./dependenciesFlatten.generated.js");
var _dependenciesImGenerated = require("./dependenciesIm.generated.js");
var _dependenciesInvGenerated = require("./dependenciesInv.generated.js");
var _dependenciesLargerGenerated = require("./dependenciesLarger.generated.js");
var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");
var _dependenciesMatrixFromColumnsGenerated = require("./dependenciesMatrixFromColumns.generated.js");
var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");
var _dependenciesMultiplyScalarGenerated = require("./dependenciesMultiplyScalar.generated.js");
var _dependenciesNumberGenerated = require("./dependenciesNumber.generated.js");
var _dependenciesQrGenerated = require("./dependenciesQr.generated.js");
var _dependenciesReGenerated = require("./dependenciesRe.generated.js");
var _dependenciesReshapeGenerated = require("./dependenciesReshape.generated.js");
var _dependenciesSinGenerated = require("./dependenciesSin.generated.js");
var _dependenciesSizeGenerated = require("./dependenciesSize.generated.js");
var _dependenciesSmallerGenerated = require("./dependenciesSmaller.generated.js");
var _dependenciesSqrtGenerated = require("./dependenciesSqrt.generated.js");
var _dependenciesSubtractGenerated = require("./dependenciesSubtract.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _dependenciesUsolveGenerated = require("./dependenciesUsolve.generated.js");
var _dependenciesUsolveAllGenerated = require("./dependenciesUsolveAll.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var eigsDependencies = exports.eigsDependencies = {
  absDependencies: _dependenciesAbsGenerated.absDependencies,
  addDependencies: _dependenciesAddGenerated.addDependencies,
  addScalarDependencies: _dependenciesAddScalarGenerated.addScalarDependencies,
  atanDependencies: _dependenciesAtanGenerated.atanDependencies,
  bignumberDependencies: _dependenciesBignumberGenerated.bignumberDependencies,
  columnDependencies: _dependenciesColumnGenerated.columnDependencies,
  complexDependencies: _dependenciesComplexGenerated.complexDependencies,
  cosDependencies: _dependenciesCosGenerated.cosDependencies,
  diagDependencies: _dependenciesDiagGenerated.diagDependencies,
  divideScalarDependencies: _dependenciesDivideScalarGenerated.divideScalarDependencies,
  dotDependencies: _dependenciesDotGenerated.dotDependencies,
  equalDependencies: _dependenciesEqualGenerated.equalDependencies,
  flattenDependencies: _dependenciesFlattenGenerated.flattenDependencies,
  imDependencies: _dependenciesImGenerated.imDependencies,
  invDependencies: _dependenciesInvGenerated.invDependencies,
  largerDependencies: _dependenciesLargerGenerated.largerDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  matrixFromColumnsDependencies: _dependenciesMatrixFromColumnsGenerated.matrixFromColumnsDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  multiplyScalarDependencies: _dependenciesMultiplyScalarGenerated.multiplyScalarDependencies,
  numberDependencies: _dependenciesNumberGenerated.numberDependencies,
  qrDependencies: _dependenciesQrGenerated.qrDependencies,
  reDependencies: _dependenciesReGenerated.reDependencies,
  reshapeDependencies: _dependenciesReshapeGenerated.reshapeDependencies,
  sinDependencies: _dependenciesSinGenerated.sinDependencies,
  sizeDependencies: _dependenciesSizeGenerated.sizeDependencies,
  smallerDependencies: _dependenciesSmallerGenerated.smallerDependencies,
  sqrtDependencies: _dependenciesSqrtGenerated.sqrtDependencies,
  subtractDependencies: _dependenciesSubtractGenerated.subtractDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  usolveDependencies: _dependenciesUsolveGenerated.usolveDependencies,
  usolveAllDependencies: _dependenciesUsolveAllGenerated.usolveAllDependencies,
  createEigs: _factoriesAny.createEigs
};