"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.corrDependencies = void 0;
var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");
var _dependenciesDivideGenerated = require("./dependenciesDivide.generated.js");
var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");
var _dependenciesMeanGenerated = require("./dependenciesMean.generated.js");
var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");
var _dependenciesPowGenerated = require("./dependenciesPow.generated.js");
var _dependenciesSqrtGenerated = require("./dependenciesSqrt.generated.js");
var _dependenciesSubtractGenerated = require("./dependenciesSubtract.generated.js");
var _dependenciesSumGenerated = require("./dependenciesSum.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var corrDependencies = exports.corrDependencies = {
  addDependencies: _dependenciesAddGenerated.addDependencies,
  divideDependencies: _dependenciesDivideGenerated.divideDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  meanDependencies: _dependenciesMeanGenerated.meanDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  powDependencies: _dependenciesPowGenerated.powDependencies,
  sqrtDependencies: _dependenciesSqrtGenerated.sqrtDependencies,
  subtractDependencies: _dependenciesSubtractGenerated.subtractDependencies,
  sumDependencies: _dependenciesSumGenerated.sumDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createCorr: _factoriesAny.createCorr
};