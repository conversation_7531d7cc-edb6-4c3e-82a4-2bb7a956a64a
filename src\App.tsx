import React, { useState } from 'react';
import { Layout, Menu, Typography, Space, Divider, BackTop } from 'antd';
import {
  FileTextOutlined,
  TableOutlined,
  LineChartOutlined,
  BarChartOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import FileUpload from './components/FileUpload';
import DataTable from './components/DataTable';
import TrajectoryChart from './components/TrajectoryChart';
import StatisticsChart from './components/StatisticsChart';
import { useAppStore } from './stores/useAppStore';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;

type MenuKey = 'upload' | 'table' | 'trajectory' | 'statistics';

const App: React.FC = () => {
  const [selectedMenu, setSelectedMenu] = useState<MenuKey>('upload');
  const [collapsed, setCollapsed] = useState(false);
  const { records } = useAppStore();

  // 菜单项配置
  const menuItems = [
    {
      key: 'upload',
      icon: <UploadOutlined />,
      label: '文件管理',
    },
    {
      key: 'table',
      icon: <TableOutlined />,
      label: '数据表格',
      disabled: records.length === 0,
    },
    {
      key: 'trajectory',
      icon: <LineChartOutlined />,
      label: '轨迹绘制',
      disabled: records.length === 0,
    },
    {
      key: 'statistics',
      icon: <BarChartOutlined />,
      label: '统计分析',
      disabled: records.length === 0,
    },
  ];

  // 渲染内容区域
  const renderContent = () => {
    switch (selectedMenu) {
      case 'upload':
        return <FileUpload />;
      case 'table':
        return <DataTable />;
      case 'trajectory':
        return <TrajectoryChart />;
      case 'statistics':
        return <StatisticsChart />;
      default:
        return <FileUpload />;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sider
        collapsible
        collapsed={collapsed}
        onCollapse={setCollapsed}
        width={250}
        style={{
          background: '#fff',
          boxShadow: '2px 0 8px 0 rgba(29, 35, 41, 0.05)',
        }}
      >
        {/* Logo区域 */}
        <div className="p-4 text-center border-b border-gray-100">
          <div className="flex items-center justify-center space-x-2">
            <FileTextOutlined className="text-2xl text-blue-500" />
            {!collapsed && (
              <div>
                <div className="font-bold text-lg text-gray-800">设备日志</div>
                <div className="text-xs text-gray-500">数据分析系统</div>
              </div>
            )}
          </div>
        </div>

        {/* 导航菜单 */}
        <Menu
          mode="inline"
          selectedKeys={[selectedMenu]}
          items={menuItems}
          style={{ border: 'none', marginTop: 16 }}
          onClick={({ key }) => setSelectedMenu(key as MenuKey)}
        />

        {/* 数据统计信息 */}
        {!collapsed && records.length > 0 && (
          <div className="p-4 mt-4 mx-4 bg-blue-50 rounded-lg">
            <div className="text-sm font-medium text-blue-800 mb-2">
              数据概览
            </div>
            <div className="space-y-1 text-xs text-blue-600">
              <div>总记录: {records.length}</div>
              <div>
                STU1: {records.filter(r => r.stageNum === 'STU1').length}
              </div>
              <div>
                STU2: {records.filter(r => r.stageNum === 'STU2').length}
              </div>
            </div>
          </div>
        )}
      </Sider>

      {/* 主内容区域 */}
      <Layout>
        {/* 顶部导航 */}
        <Header
          style={{
            background: '#fff',
            padding: '0 24px',
            boxShadow: '0 2px 8px 0 rgba(29, 35, 41, 0.05)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <div>
            <Title level={4} style={{ margin: 0, color: '#1f2937' }}>
              {menuItems.find(item => item.key === selectedMenu)?.label}
            </Title>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {selectedMenu === 'upload' && '上传和管理日志文件'}
              {selectedMenu === 'table' && '查看和筛选数据记录'}
              {selectedMenu === 'trajectory' && '绘制设备运行轨迹'}
              {selectedMenu === 'statistics' && '分析数据统计分布'}
            </Text>
          </div>

          <Space>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              设备作业日志数据分析系统 v1.0
            </Text>
          </Space>
        </Header>

        {/* 内容区域 */}
        <Content
          style={{
            margin: '24px',
            padding: '24px',
            background: '#fff',
            borderRadius: '8px',
            boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
            minHeight: 'calc(100vh - 112px)',
          }}
        >
          {renderContent()}
        </Content>
      </Layout>

      {/* 回到顶部按钮 */}
      <BackTop />
    </Layout>
  );
};

export default App;
