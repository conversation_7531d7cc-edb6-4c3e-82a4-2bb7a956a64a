"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.fixDependencies = void 0;
var _dependenciesComplexClassGenerated = require("./dependenciesComplexClass.generated.js");
var _dependenciesDenseMatrixClassGenerated = require("./dependenciesDenseMatrixClass.generated.js");
var _dependenciesCeilGenerated = require("./dependenciesCeil.generated.js");
var _dependenciesEqualScalarGenerated = require("./dependenciesEqualScalar.generated.js");
var _dependenciesFloorGenerated = require("./dependenciesFloor.generated.js");
var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _dependenciesZerosGenerated = require("./dependenciesZeros.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var fixDependencies = exports.fixDependencies = {
  ComplexDependencies: _dependenciesComplexClassGenerated.ComplexDependencies,
  DenseMatrixDependencies: _dependenciesDenseMatrixClassGenerated.DenseMatrixDependencies,
  ceilDependencies: _dependenciesCeilGenerated.ceilDependencies,
  equalScalarDependencies: _dependenciesEqualScalarGenerated.equalScalarDependencies,
  floorDependencies: _dependenciesFloorGenerated.floorDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  zerosDependencies: _dependenciesZerosGenerated.zerosDependencies,
  createFix: _factoriesAny.createFix
};