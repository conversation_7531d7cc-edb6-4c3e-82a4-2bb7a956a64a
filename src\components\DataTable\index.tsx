import React, { useMemo, useState } from 'react';
import { Table, Card, Tag, Button, Space, Input, Select, Tooltip } from 'antd';
import { SearchOutlined, FilterOutlined, EyeOutlined } from '@ant-design/icons';
import type { ColumnsType, TableProps } from 'antd/es/table';
import { LogRecord } from '@/types';
import { useAppStore } from '@/stores/useAppStore';
import { MathUtils } from '@/utils/mathUtils';

const { Search } = Input;
const { Option } = Select;

interface DataTableProps {
  onRecordSelect?: (record: LogRecord) => void;
}

const DataTable: React.FC<DataTableProps> = ({ onRecordSelect }) => {
  const { records, selectedRecord, setSelectedRecord } = useAppStore();
  const [searchText, setSearchText] = useState('');
  const [stageFilter, setStageFilter] = useState<string>('all');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
  });

  // 过滤数据
  const filteredData = useMemo(() => {
    let filtered = records;

    // 机台筛选
    if (stageFilter !== 'all') {
      filtered = filtered.filter(record => record.stageNum === stageFilter);
    }

    // 搜索筛选
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      filtered = filtered.filter(
        record =>
          record.id.toLowerCase().includes(searchLower) ||
          record.stageNum.toLowerCase().includes(searchLower) ||
          record.timestamp.toLowerCase().includes(searchLower)
      );
    }

    return filtered;
  }, [records, stageFilter, searchText]);

  // 表格列定义
  const columns: ColumnsType<LogRecord> = [
    {
      title: '序号',
      key: 'index',
      width: 60,
      render: (_, __, index) =>
        (pagination.current - 1) * pagination.pageSize + index + 1,
      fixed: 'left',
    },
    {
      title: '时间戳',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      sorter: (a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
      render: (timestamp: string) => (
        <span className="text-sm text-gray-600">
          {new Date(timestamp).toLocaleString('zh-CN')}
        </span>
      ),
    },
    {
      title: '机台号',
      dataIndex: 'stageNum',
      key: 'stageNum',
      width: 80,
      filters: [
        { text: 'STU1', value: 'STU1' },
        { text: 'STU2', value: 'STU2' },
      ],
      onFilter: (value, record) => record.stageNum === value,
      render: (stageNum: string) => (
        <Tag color={stageNum === 'STU1' ? 'blue' : 'green'}>{stageNum}</Tag>
      ),
    },
    {
      title: 'D1坐标',
      key: 'd1',
      width: 120,
      render: (_, record) => (
        <span className="text-xs">
          ({MathUtils.formatNumber(record.d1.x)},{' '}
          {MathUtils.formatNumber(record.d1.y)})
        </span>
      ),
    },
    {
      title: 'D2坐标',
      key: 'd2',
      width: 120,
      render: (_, record) => (
        <span className="text-xs">
          ({MathUtils.formatNumber(record.d2.x)},{' '}
          {MathUtils.formatNumber(record.d2.y)})
        </span>
      ),
    },
    {
      title: 'D3坐标',
      key: 'd3',
      width: 120,
      render: (_, record) => (
        <span className="text-xs">
          ({MathUtils.formatNumber(record.d3.x)},{' '}
          {MathUtils.formatNumber(record.d3.y)})
        </span>
      ),
    },
    {
      title: 'Ra',
      dataIndex: 'ra',
      key: 'ra',
      width: 80,
      sorter: (a, b) => a.ra - b.ra,
      render: (ra: number) => MathUtils.formatNumber(ra),
    },
    {
      title: 'D4坐标',
      key: 'd4',
      width: 120,
      render: (_, record) => (
        <span className="text-xs">
          ({MathUtils.formatNumber(record.d4.x)},{' '}
          {MathUtils.formatNumber(record.d4.y)})
        </span>
      ),
    },
    {
      title: 'D5坐标',
      key: 'd5',
      width: 120,
      render: (_, record) => (
        <span className="text-xs">
          ({MathUtils.formatNumber(record.d5.x)},{' '}
          {MathUtils.formatNumber(record.d5.y)})
        </span>
      ),
    },
    {
      title: 'Rb',
      dataIndex: 'rb',
      key: 'rb',
      width: 80,
      sorter: (a, b) => a.rb - b.rb,
      render: (rb: number) => MathUtils.formatNumber(rb),
    },
    {
      title: 'D6坐标',
      key: 'd6',
      width: 120,
      render: (_, record) => (
        <span className="text-xs">
          ({MathUtils.formatNumber(record.d6.x)},{' '}
          {MathUtils.formatNumber(record.d6.y)})
        </span>
      ),
    },
    {
      title: 'm1',
      dataIndex: 'm1',
      key: 'm1',
      width: 100,
      sorter: (a, b) => a.m1 - b.m1,
      render: (m1: number) => MathUtils.formatNumber(m1),
    },
    {
      title: 'm2',
      dataIndex: 'm2',
      key: 'm2',
      width: 100,
      sorter: (a, b) => a.m2 - b.m2,
      render: (m2: number) => MathUtils.formatNumber(m2, 6),
    },
    {
      title: 'm3',
      dataIndex: 'm3',
      key: 'm3',
      width: 100,
      sorter: (a, b) => a.m3 - b.m3,
      render: (m3: number) => MathUtils.formatNumber(m3),
    },
    {
      title: 'tanValue1',
      dataIndex: 'tanValue1',
      key: 'tanValue1',
      width: 120,
      sorter: (a, b) => a.tanValue1 - b.tanValue1,
      render: (tanValue1: number) => (
        <span className="font-medium text-blue-600">
          {MathUtils.formatNumber(tanValue1)}
        </span>
      ),
    },
    {
      title: 'tanValue2',
      dataIndex: 'tanValue2',
      key: 'tanValue2',
      width: 120,
      sorter: (a, b) => a.tanValue2 - b.tanValue2,
      render: (tanValue2: number) => (
        <span className="font-medium text-green-600">
          {MathUtils.formatNumber(tanValue2)}
        </span>
      ),
    },
    {
      title: '面积 (mm²)',
      dataIndex: 'area',
      key: 'area',
      width: 120,
      sorter: (a, b) => (a.area || 0) - (b.area || 0),
      render: (area: number) => (
        <span className="font-medium text-purple-600">
          {MathUtils.formatNumber(area || 0)}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      fixed: 'right',
      render: (_, record) => (
        <Tooltip title="查看轨迹">
          <Button
            type="text"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleRecordSelect(record)}
            className="text-blue-500 hover:text-blue-700"
          />
        </Tooltip>
      ),
    },
  ];

  // 处理记录选择
  const handleRecordSelect = (record: LogRecord) => {
    setSelectedRecord(record);
    onRecordSelect?.(record);
  };

  // 表格配置
  const tableProps: TableProps<LogRecord> = {
    columns,
    dataSource: filteredData,
    rowKey: 'id',
    pagination: {
      ...pagination,
      total: filteredData.length,
    },
    scroll: { x: 1800, y: 600 },
    size: 'small',
    rowSelection: {
      type: 'radio',
      selectedRowKeys: selectedRecord ? [selectedRecord.id] : [],
      onChange: (selectedRowKeys, selectedRows) => {
        const record = selectedRows[0] || null;
        handleRecordSelect(record);
      },
    },
    rowClassName: record =>
      record.id === selectedRecord?.id ? 'bg-blue-50' : '',
    onChange: paginationConfig => {
      setPagination(prev => ({
        ...prev,
        current: paginationConfig.current || 1,
        pageSize: paginationConfig.pageSize || 20,
      }));
    },
  };

  return (
    <Card
      title="数据表格"
      className="fade-in"
      extra={
        <Space>
          <Search
            placeholder="搜索记录..."
            allowClear
            style={{ width: 200 }}
            onChange={e => setSearchText(e.target.value)}
            prefix={<SearchOutlined />}
          />
          <Select
            value={stageFilter}
            style={{ width: 120 }}
            onChange={setStageFilter}
            prefix={<FilterOutlined />}
          >
            <Option value="all">全部机台</Option>
            <Option value="STU1">STU1</Option>
            <Option value="STU2">STU2</Option>
          </Select>
        </Space>
      }
    >
      <Table {...tableProps} />
    </Card>
  );
};

export default DataTable;
