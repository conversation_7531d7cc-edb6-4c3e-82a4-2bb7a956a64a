import { LogRecord } from '@/types';
import { v4 as uuidv4 } from 'uuid';

/**
 * 生成演示数据
 */
export const generateDemoData = (count: number = 50): LogRecord[] => {
  const records: LogRecord[] = [];
  const baseTime = new Date('2025-08-05 00:00:00');

  for (let i = 0; i < count; i++) {
    const stageNum = Math.random() > 0.5 ? 'STU1' : 'STU2';
    const timestamp = new Date(baseTime.getTime() + i * 18000).toISOString(); // 每18秒一条记录

    // 根据机台生成不同的坐标范围
    const baseX = stageNum === 'STU1' ? 800 : 1700;
    const baseY = 700;

    // 生成六个坐标点（模拟真实的轨迹）
    const d1 = {
      x: baseX + (Math.random() - 0.5) * 10,
      y: baseY + (Math.random() - 0.5) * 20,
    };

    const d2 = {
      x: d1.x + (Math.random() - 0.5) * 5,
      y: d1.y + 300 + (Math.random() - 0.5) * 20,
    };

    const d3 = {
      x: d2.x - 2 + (Math.random() - 0.5) * 2,
      y: d2.y + 2 + (Math.random() - 0.5) * 2,
    };

    const d4 = {
      x: d3.x - 530 + (Math.random() - 0.5) * 10,
      y: d3.y + (Math.random() - 0.5) * 5,
    };

    const d5 = {
      x: d4.x - 2 + (Math.random() - 0.5) * 2,
      y: d4.y - 2 + (Math.random() - 0.5) * 2,
    };

    const d6 = {
      x: d5.x + (Math.random() - 0.5) * 5,
      y: d1.y + (Math.random() - 0.5) * 20,
    };

    // 生成半径值
    const ra = 2.4 + (Math.random() - 0.5) * 0.2;
    const rb = 2.4 + (Math.random() - 0.5) * 0.2;

    // 生成计算参数
    const m1 = 1000 + Math.random() * 5000;
    const m2 = (Math.random() - 0.5) * 0.001;
    const m3 = 1500 + Math.random() * 3000;
    const tanValue1 = 1500 + Math.random() * 3000;
    const tanValue2 = 2000 + Math.random() * 4000;
    const tanSpec = 977.022835;

    // 计算面积（使用鞋带公式）
    const points = [d1, d2, d3, d4, d5, d6];
    let area = 0;
    const n = points.length;
    for (let j = 0; j < n; j++) {
      const k = (j + 1) % n;
      area += points[j].x * points[k].y;
      area -= points[k].x * points[j].y;
    }
    area = Math.abs(area) / 2;

    const record: LogRecord = {
      id: uuidv4(),
      timestamp,
      stageNum,
      d1,
      d2,
      d3,
      ra,
      d4,
      d5,
      rb,
      d6,
      m1,
      m2,
      m3,
      tanValue1,
      tanValue2,
      tanSpec,
      area,
    };

    records.push(record);
  }

  return records;
};

/**
 * 生成演示日志文件内容
 */
export const generateDemoLogContent = (count: number = 10): string => {
  const lines: string[] = [];
  const baseTime = new Date('2025-08-05 00:00:00');

  for (let i = 0; i < count; i++) {
    const stageNum = Math.random() > 0.5 ? 'STU1' : 'STU2';
    const timestamp = new Date(baseTime.getTime() + i * 18000);
    const timeStr = timestamp.toISOString().slice(0, 19).replace('T', ' ');

    // 根据机台生成不同的坐标范围
    const baseX = stageNum === 'STU1' ? 800 : 1700;
    const baseY = 700;
    const zValue = stageNum === 'STU1' ? 16.0 : 15.8;
    const yPrefix = stageNum === 'STU1' ? 'Y' : 'YY';

    // 生成六个坐标点
    const d1 = {
      x: baseX + (Math.random() - 0.5) * 10,
      y: baseY + (Math.random() - 0.5) * 20,
    };

    const d2 = {
      x: d1.x + (Math.random() - 0.5) * 5,
      y: d1.y + 300 + (Math.random() - 0.5) * 20,
    };

    const d3 = {
      x: d2.x - 2 + (Math.random() - 0.5) * 2,
      y: d2.y + 2 + (Math.random() - 0.5) * 2,
    };

    const d4 = {
      x: d3.x - 530 + (Math.random() - 0.5) * 10,
      y: d3.y + (Math.random() - 0.5) * 5,
    };

    const d5 = {
      x: d4.x - 2 + (Math.random() - 0.5) * 2,
      y: d4.y - 2 + (Math.random() - 0.5) * 2,
    };

    const d6 = {
      x: d5.x + (Math.random() - 0.5) * 5,
      y: d1.y + (Math.random() - 0.5) * 20,
    };

    // 生成半径值
    const ra = 2.4 + (Math.random() - 0.5) * 0.2;
    const rb = 2.4 + (Math.random() - 0.5) * 0.2;

    // 生成计算参数
    const m1 = 1000 + Math.random() * 5000;
    const m2 = (Math.random() - 0.5) * 0.001;
    const m3 = 1500 + Math.random() * 3000;
    const tanValue1 = 1500 + Math.random() * 3000;
    const tanValue2 = 2000 + Math.random() * 4000;
    const tanSpec = 977.022835;

    // 生成日志行
    lines.push(
      `[${timeStr}] [000] [8-DEBUG] ${stageNum}   (MCtrlStage.cpp, ${stageNum === 'STU1' ? '3558' : '3574'})`
    );
    lines.push(
      `[${timeStr}] [000] [8-DEBUG] G00 X${d1.x.toFixed(6)} ${yPrefix}${d1.y.toFixed(6)} Z${zValue.toFixed(6)}   (MCtrlStage.cpp, 3643)`
    );
    lines.push(
      `[${timeStr}] [000] [8-DEBUG] G01 X${d2.x.toFixed(6)} ${yPrefix}${d2.y.toFixed(6)} Z${zValue.toFixed(6)}   (MCtrlStage.cpp, 3656)`
    );
    lines.push(
      `[${timeStr}] [000] [8-DEBUG] G03 X${d3.x.toFixed(6)} ${yPrefix}${d3.y.toFixed(6)} R${ra.toFixed(6)}   (MCtrlStage.cpp, 3626)`
    );
    lines.push(
      `[${timeStr}] [000] [8-DEBUG] G01 X${d4.x.toFixed(6)} ${yPrefix}${d4.y.toFixed(6)} Z${zValue.toFixed(6)}   (MCtrlStage.cpp, 3656)`
    );
    lines.push(
      `[${timeStr}] [000] [8-DEBUG] G03 X${d5.x.toFixed(6)} ${yPrefix}${d5.y.toFixed(6)} R${rb.toFixed(6)}   (MCtrlStage.cpp, 3626)`
    );
    lines.push(
      `[${timeStr}] [000] [8-DEBUG] G01 X${d6.x.toFixed(6)} ${yPrefix}${d6.y.toFixed(6)} Z${zValue.toFixed(6)}   (MCtrlStage.cpp, 3656)`
    );
    lines.push(
      `[${timeStr}] [000] [8-DEBUG] m1 ${m1.toFixed(6)}, m2 ${m2.toFixed(6)}, m3 ${m3.toFixed(6)}, tanValue1 ${tanValue1.toFixed(6)}, tanValue2 ${tanValue2.toFixed(6)}, tanSpec ${tanSpec.toFixed(6)}   (MCtrlStage.cpp, 3716)`
    );
    lines.push(
      `[${timeStr}] [000] [8-DEBUG] ------------------------------------------------------------   (MCtrlStage.cpp, 3718)`
    );
  }

  return lines.join('\n');
};

/**
 * 下载演示日志文件
 */
export const downloadDemoLogFile = (count: number = 50) => {
  const content = generateDemoLogContent(count);
  const blob = new Blob([content], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = `demo_log_${count}_records.log`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};
