"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.varianceTransformDependencies = void 0;
var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");
var _dependenciesApplyGenerated = require("./dependenciesApply.generated.js");
var _dependenciesDivideGenerated = require("./dependenciesDivide.generated.js");
var _dependenciesIsNaNGenerated = require("./dependenciesIsNaN.generated.js");
var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");
var _dependenciesSubtractGenerated = require("./dependenciesSubtract.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var varianceTransformDependencies = exports.varianceTransformDependencies = {
  addDependencies: _dependenciesAddGenerated.addDependencies,
  applyDependencies: _dependenciesApplyGenerated.applyDependencies,
  divideDependencies: _dependenciesDivideGenerated.divideDependencies,
  isNaNDependencies: _dependenciesIsNaNGenerated.isNaNDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  subtractDependencies: _dependenciesSubtractGenerated.subtractDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createVarianceTransform: _factoriesAny.createVarianceTransform
};