import React, { useMemo } from 'react';
import { Card, Empty, Statistic, Row, Col, Tag, Divider } from 'antd';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { LogRecord, Point } from '@/types';
import { useAppStore } from '@/stores/useAppStore';
import { MathUtils } from '@/utils/mathUtils';

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface TrajectoryChartProps {
  className?: string;
}

const TrajectoryChart: React.FC<TrajectoryChartProps> = ({ className }) => {
  const { selectedRecord } = useAppStore();

  // 计算轨迹数据
  const trajectoryData = useMemo(() => {
    if (!selectedRecord) return null;

    const points: Point[] = [
      selectedRecord.d1,
      selectedRecord.d2,
      selectedRecord.d3,
      selectedRecord.d4,
      selectedRecord.d5,
      selectedRecord.d6,
      selectedRecord.d1, // 闭合图形
    ];

    const area = MathUtils.calculateTrajectoryArea(
      selectedRecord.d1,
      selectedRecord.d2,
      selectedRecord.d3,
      selectedRecord.d4,
      selectedRecord.d5,
      selectedRecord.d6
    );

    const perimeter = MathUtils.calculateTrajectoryLength([
      selectedRecord.d1,
      selectedRecord.d2,
      selectedRecord.d3,
      selectedRecord.d4,
      selectedRecord.d5,
      selectedRecord.d6,
    ]);

    return {
      points,
      area,
      perimeter,
      record: selectedRecord,
    };
  }, [selectedRecord]);

  // Chart.js配置
  const chartData = useMemo(() => {
    if (!trajectoryData) return null;

    const { points } = trajectoryData;

    return {
      datasets: [
        {
          label: '轨迹路径',
          data: points.map(point => ({ x: point.x, y: point.y })),
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 2,
          pointBackgroundColor: 'rgb(59, 130, 246)',
          pointBorderColor: 'white',
          pointBorderWidth: 2,
          pointRadius: 6,
          pointHoverRadius: 8,
          fill: true,
          tension: 0,
        },
        {
          label: '坐标点',
          data: points.slice(0, 6).map((point, index) => ({
            x: point.x,
            y: point.y,
            label: `D${index + 1}`,
          })),
          borderColor: 'rgb(239, 68, 68)',
          backgroundColor: 'rgb(239, 68, 68)',
          borderWidth: 0,
          pointBackgroundColor: 'rgb(239, 68, 68)',
          pointBorderColor: 'white',
          pointBorderWidth: 2,
          pointRadius: 8,
          pointHoverRadius: 10,
          showLine: false,
        },
      ],
    };
  }, [trajectoryData]);

  const chartOptions = useMemo(
    () => ({
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: true,
          text: `轨迹图 - ${trajectoryData?.record.stageNum || ''}`,
          font: {
            size: 16,
            weight: 'bold' as const,
          },
        },
        tooltip: {
          callbacks: {
            label: (context: any) => {
              const point = context.parsed;
              const datasetIndex = context.datasetIndex;

              if (datasetIndex === 1) {
                // 坐标点
                const pointIndex = context.dataIndex;
                return `D${pointIndex + 1}: (${MathUtils.formatNumber(point.x)}, ${MathUtils.formatNumber(point.y)})`;
              } else {
                // 轨迹路径
                return `坐标: (${MathUtils.formatNumber(point.x)}, ${MathUtils.formatNumber(point.y)})`;
              }
            },
          },
        },
      },
      scales: {
        x: {
          type: 'linear' as const,
          position: 'bottom' as const,
          title: {
            display: true,
            text: 'X坐标 (mm)',
            font: {
              size: 14,
              weight: 'bold' as const,
            },
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)',
          },
        },
        y: {
          type: 'linear' as const,
          title: {
            display: true,
            text: 'Y坐标 (mm)',
            font: {
              size: 14,
              weight: 'bold' as const,
            },
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)',
          },
        },
      },
      elements: {
        point: {
          hoverBorderWidth: 3,
        },
      },
      interaction: {
        intersect: false,
        mode: 'point' as const,
      },
    }),
    [trajectoryData]
  );

  if (!selectedRecord) {
    return (
      <Card title="轨迹绘制" className={`fade-in ${className || ''}`}>
        <Empty
          description="请从数据表格中选择一条记录来查看轨迹图"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  if (!trajectoryData || !chartData) {
    return (
      <Card title="轨迹绘制" className={`fade-in ${className || ''}`}>
        <Empty description="无法生成轨迹数据" />
      </Card>
    );
  }

  return (
    <Card
      title="轨迹绘制"
      className={`fade-in ${className || ''}`}
      extra={
        <Tag color={selectedRecord.stageNum === 'STU1' ? 'blue' : 'green'}>
          {selectedRecord.stageNum}
        </Tag>
      }
    >
      <Row gutter={[16, 16]}>
        {/* 统计信息 */}
        <Col span={24}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="封闭图形面积"
                value={trajectoryData.area}
                suffix="mm²"
                precision={2}
                valueStyle={{ color: '#3f8600' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="轨迹周长"
                value={trajectoryData.perimeter}
                suffix="mm"
                precision={2}
                valueStyle={{ color: '#1890ff' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="tanValue1"
                value={selectedRecord.tanValue1}
                precision={2}
                valueStyle={{ color: '#722ed1' }}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="tanValue2"
                value={selectedRecord.tanValue2}
                precision={2}
                valueStyle={{ color: '#eb2f96' }}
              />
            </Col>
          </Row>
        </Col>

        <Col span={24}>
          <Divider />
        </Col>

        {/* 轨迹图表 */}
        <Col span={24}>
          <div style={{ height: '500px' }}>
            <Line data={chartData} options={chartOptions} />
          </div>
        </Col>

        {/* 坐标点详情 */}
        <Col span={24}>
          <Divider orientation="left">坐标点详情</Divider>
          <Row gutter={[8, 8]}>
            {[
              { label: 'D1', point: selectedRecord.d1 },
              { label: 'D2', point: selectedRecord.d2 },
              {
                label: 'D3',
                point: selectedRecord.d3,
                extra: `Ra: ${MathUtils.formatNumber(selectedRecord.ra)}`,
              },
              { label: 'D4', point: selectedRecord.d4 },
              {
                label: 'D5',
                point: selectedRecord.d5,
                extra: `Rb: ${MathUtils.formatNumber(selectedRecord.rb)}`,
              },
              { label: 'D6', point: selectedRecord.d6 },
            ].map(({ label, point, extra }) => (
              <Col span={4} key={label}>
                <Card size="small" className="text-center">
                  <div className="font-semibold text-blue-600">{label}</div>
                  <div className="text-xs text-gray-600">
                    X: {MathUtils.formatNumber(point.x)}
                  </div>
                  <div className="text-xs text-gray-600">
                    Y: {MathUtils.formatNumber(point.y)}
                  </div>
                  {extra && (
                    <div className="text-xs text-orange-600 mt-1">{extra}</div>
                  )}
                </Card>
              </Col>
            ))}
          </Row>
        </Col>
      </Row>
    </Card>
  );
};

export default TrajectoryChart;
