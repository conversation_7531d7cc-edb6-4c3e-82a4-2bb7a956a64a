"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.compositionDependencies = void 0;
var _dependenciesAddScalarGenerated = require("./dependenciesAddScalar.generated.js");
var _dependenciesCombinationsGenerated = require("./dependenciesCombinations.generated.js");
var _dependenciesIsIntegerGenerated = require("./dependenciesIsInteger.generated.js");
var _dependenciesIsNegativeGenerated = require("./dependenciesIsNegative.generated.js");
var _dependenciesIsPositiveGenerated = require("./dependenciesIsPositive.generated.js");
var _dependenciesLargerGenerated = require("./dependenciesLarger.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var compositionDependencies = exports.compositionDependencies = {
  addScalarDependencies: _dependenciesAddScalarGenerated.addScalarDependencies,
  combinationsDependencies: _dependenciesCombinationsGenerated.combinationsDependencies,
  isIntegerDependencies: _dependenciesIsIntegerGenerated.isIntegerDependencies,
  isNegativeDependencies: _dependenciesIsNegativeGenerated.isNegativeDependencies,
  isPositiveDependencies: _dependenciesIsPositiveGenerated.isPositiveDependencies,
  largerDependencies: _dependenciesLargerGenerated.largerDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createComposition: _factoriesAny.createComposition
};