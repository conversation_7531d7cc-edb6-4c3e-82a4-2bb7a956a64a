"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.sqrtmDependencies = void 0;
var _dependenciesAbsGenerated = require("./dependenciesAbs.generated.js");
var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");
var _dependenciesIdentityGenerated = require("./dependenciesIdentity.generated.js");
var _dependenciesInvGenerated = require("./dependenciesInv.generated.js");
var _dependenciesMapGenerated = require("./dependenciesMap.generated.js");
var _dependenciesMaxGenerated = require("./dependenciesMax.generated.js");
var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");
var _dependenciesSizeGenerated = require("./dependenciesSize.generated.js");
var _dependenciesSqrtGenerated = require("./dependenciesSqrt.generated.js");
var _dependenciesSubtractGenerated = require("./dependenciesSubtract.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var sqrtmDependencies = exports.sqrtmDependencies = {
  absDependencies: _dependenciesAbsGenerated.absDependencies,
  addDependencies: _dependenciesAddGenerated.addDependencies,
  identityDependencies: _dependenciesIdentityGenerated.identityDependencies,
  invDependencies: _dependenciesInvGenerated.invDependencies,
  mapDependencies: _dependenciesMapGenerated.mapDependencies,
  maxDependencies: _dependenciesMaxGenerated.maxDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  sizeDependencies: _dependenciesSizeGenerated.sizeDependencies,
  sqrtDependencies: _dependenciesSqrtGenerated.sqrtDependencies,
  subtractDependencies: _dependenciesSubtractGenerated.subtractDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createSqrtm: _factoriesAny.createSqrtm
};