"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.zetaDependencies = void 0;
var _dependenciesBigNumberClassGenerated = require("./dependenciesBigNumberClass.generated.js");
var _dependenciesComplexClassGenerated = require("./dependenciesComplexClass.generated.js");
var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");
var _dependenciesDivideGenerated = require("./dependenciesDivide.generated.js");
var _dependenciesEqualGenerated = require("./dependenciesEqual.generated.js");
var _dependenciesFactorialGenerated = require("./dependenciesFactorial.generated.js");
var _dependenciesGammaGenerated = require("./dependenciesGamma.generated.js");
var _dependenciesIsNegativeGenerated = require("./dependenciesIsNegative.generated.js");
var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");
var _dependenciesPiGenerated = require("./dependenciesPi.generated.js");
var _dependenciesPowGenerated = require("./dependenciesPow.generated.js");
var _dependenciesSinGenerated = require("./dependenciesSin.generated.js");
var _dependenciesSmallerEqGenerated = require("./dependenciesSmallerEq.generated.js");
var _dependenciesSubtractGenerated = require("./dependenciesSubtract.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var zetaDependencies = exports.zetaDependencies = {
  BigNumberDependencies: _dependenciesBigNumberClassGenerated.BigNumberDependencies,
  ComplexDependencies: _dependenciesComplexClassGenerated.ComplexDependencies,
  addDependencies: _dependenciesAddGenerated.addDependencies,
  divideDependencies: _dependenciesDivideGenerated.divideDependencies,
  equalDependencies: _dependenciesEqualGenerated.equalDependencies,
  factorialDependencies: _dependenciesFactorialGenerated.factorialDependencies,
  gammaDependencies: _dependenciesGammaGenerated.gammaDependencies,
  isNegativeDependencies: _dependenciesIsNegativeGenerated.isNegativeDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  piDependencies: _dependenciesPiGenerated.piDependencies,
  powDependencies: _dependenciesPowGenerated.powDependencies,
  sinDependencies: _dependenciesSinGenerated.sinDependencies,
  smallerEqDependencies: _dependenciesSmallerEqGenerated.smallerEqDependencies,
  subtractDependencies: _dependenciesSubtractGenerated.subtractDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createZeta: _factoriesAny.createZeta
};