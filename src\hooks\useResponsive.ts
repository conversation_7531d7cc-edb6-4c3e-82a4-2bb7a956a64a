import { useState, useEffect } from 'react';

interface BreakpointConfig {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
}

interface ResponsiveInfo {
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  breakpoint: keyof BreakpointConfig;
}

const defaultBreakpoints: BreakpointConfig = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600,
};

/**
 * 响应式设计Hook
 * @param breakpoints 自定义断点配置
 * @returns 响应式信息
 */
export const useResponsive = (
  breakpoints: BreakpointConfig = defaultBreakpoints
): ResponsiveInfo => {
  const [windowSize, setWindowSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    // 添加防抖处理
    let timeoutId: NodeJS.Timeout;
    const debouncedHandleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(handleResize, 150);
    };

    window.addEventListener('resize', debouncedHandleResize);

    // 初始化时获取一次尺寸
    handleResize();

    return () => {
      window.removeEventListener('resize', debouncedHandleResize);
      clearTimeout(timeoutId);
    };
  }, []);

  // 计算当前断点
  const getCurrentBreakpoint = (width: number): keyof BreakpointConfig => {
    if (width < breakpoints.xs) return 'xs';
    if (width < breakpoints.sm) return 'sm';
    if (width < breakpoints.md) return 'md';
    if (width < breakpoints.lg) return 'lg';
    if (width < breakpoints.xl) return 'xl';
    return 'xxl';
  };

  const breakpoint = getCurrentBreakpoint(windowSize.width);

  return {
    width: windowSize.width,
    height: windowSize.height,
    isMobile: windowSize.width < breakpoints.md,
    isTablet:
      windowSize.width >= breakpoints.md && windowSize.width < breakpoints.lg,
    isDesktop: windowSize.width >= breakpoints.lg,
    breakpoint,
  };
};

/**
 * 媒体查询Hook
 * @param query 媒体查询字符串
 * @returns 是否匹配
 */
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [query]);

  return matches;
};

/**
 * 获取响应式列配置
 * @param breakpoint 当前断点
 * @returns 列配置
 */
export const getResponsiveColumns = (breakpoint: keyof BreakpointConfig) => {
  const columnConfigs = {
    xs: { span: 24, gutter: [8, 8] },
    sm: { span: 12, gutter: [16, 16] },
    md: { span: 8, gutter: [16, 16] },
    lg: { span: 6, gutter: [24, 24] },
    xl: { span: 4, gutter: [24, 24] },
    xxl: { span: 4, gutter: [32, 32] },
  };

  return columnConfigs[breakpoint];
};

/**
 * 获取响应式表格配置
 * @param breakpoint 当前断点
 * @returns 表格配置
 */
export const getResponsiveTableConfig = (
  breakpoint: keyof BreakpointConfig
) => {
  const isMobile = ['xs', 'sm'].includes(breakpoint);
  const isTablet = breakpoint === 'md';

  return {
    size: isMobile ? 'small' : 'middle',
    scroll: {
      x: isMobile ? 800 : isTablet ? 1200 : 1800,
      y: isMobile ? 400 : isTablet ? 500 : 600,
    },
    pagination: {
      pageSize: isMobile ? 10 : isTablet ? 15 : 20,
      showSizeChanger: !isMobile,
      showQuickJumper: !isMobile,
      simple: isMobile,
    },
  };
};

/**
 * 获取响应式图表配置
 * @param breakpoint 当前断点
 * @returns 图表配置
 */
export const getResponsiveChartConfig = (
  breakpoint: keyof BreakpointConfig
) => {
  const isMobile = ['xs', 'sm'].includes(breakpoint);
  const isTablet = breakpoint === 'md';

  return {
    height: isMobile ? 300 : isTablet ? 400 : 500,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: isMobile ? 'bottom' : 'top',
          labels: {
            fontSize: isMobile ? 10 : 12,
          },
        },
        title: {
          font: {
            size: isMobile ? 14 : 16,
          },
        },
      },
      scales: {
        x: {
          title: {
            font: {
              size: isMobile ? 10 : 12,
            },
          },
          ticks: {
            fontSize: isMobile ? 8 : 10,
          },
        },
        y: {
          title: {
            font: {
              size: isMobile ? 10 : 12,
            },
          },
          ticks: {
            fontSize: isMobile ? 8 : 10,
          },
        },
      },
    },
  };
};
