"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.rangeTransformDependencies = void 0;
var _dependenciesBignumberGenerated = require("./dependenciesBignumber.generated.js");
var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");
var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");
var _dependenciesIsPositiveGenerated = require("./dependenciesIsPositive.generated.js");
var _dependenciesLargerGenerated = require("./dependenciesLarger.generated.js");
var _dependenciesLargerEqGenerated = require("./dependenciesLargerEq.generated.js");
var _dependenciesSmallerGenerated = require("./dependenciesSmaller.generated.js");
var _dependenciesSmallerEqGenerated = require("./dependenciesSmallerEq.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var rangeTransformDependencies = exports.rangeTransformDependencies = {
  bignumberDependencies: _dependenciesBignumberGenerated.bignumberDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  addDependencies: _dependenciesAddGenerated.addDependencies,
  isPositiveDependencies: _dependenciesIsPositiveGenerated.isPositiveDependencies,
  largerDependencies: _dependenciesLargerGenerated.largerDependencies,
  largerEqDependencies: _dependenciesLargerEqGenerated.largerEqDependencies,
  smallerDependencies: _dependenciesSmallerGenerated.smallerDependencies,
  smallerEqDependencies: _dependenciesSmallerEqGenerated.smallerEqDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createRangeTransform: _factoriesAny.createRangeTransform
};