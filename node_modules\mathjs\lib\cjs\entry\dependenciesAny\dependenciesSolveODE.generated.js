"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.solveODEDependencies = void 0;
var _dependenciesAbsGenerated = require("./dependenciesAbs.generated.js");
var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");
var _dependenciesBignumberGenerated = require("./dependenciesBignumber.generated.js");
var _dependenciesDivideGenerated = require("./dependenciesDivide.generated.js");
var _dependenciesIsNegativeGenerated = require("./dependenciesIsNegative.generated.js");
var _dependenciesIsPositiveGenerated = require("./dependenciesIsPositive.generated.js");
var _dependenciesLargerGenerated = require("./dependenciesLarger.generated.js");
var _dependenciesMapGenerated = require("./dependenciesMap.generated.js");
var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");
var _dependenciesMaxGenerated = require("./dependenciesMax.generated.js");
var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");
var _dependenciesSmallerGenerated = require("./dependenciesSmaller.generated.js");
var _dependenciesSubtractGenerated = require("./dependenciesSubtract.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _dependenciesUnaryMinusGenerated = require("./dependenciesUnaryMinus.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var solveODEDependencies = exports.solveODEDependencies = {
  absDependencies: _dependenciesAbsGenerated.absDependencies,
  addDependencies: _dependenciesAddGenerated.addDependencies,
  bignumberDependencies: _dependenciesBignumberGenerated.bignumberDependencies,
  divideDependencies: _dependenciesDivideGenerated.divideDependencies,
  isNegativeDependencies: _dependenciesIsNegativeGenerated.isNegativeDependencies,
  isPositiveDependencies: _dependenciesIsPositiveGenerated.isPositiveDependencies,
  largerDependencies: _dependenciesLargerGenerated.largerDependencies,
  mapDependencies: _dependenciesMapGenerated.mapDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  maxDependencies: _dependenciesMaxGenerated.maxDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  smallerDependencies: _dependenciesSmallerGenerated.smallerDependencies,
  subtractDependencies: _dependenciesSubtractGenerated.subtractDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  unaryMinusDependencies: _dependenciesUnaryMinusGenerated.unaryMinusDependencies,
  createSolveODE: _factoriesAny.createSolveODE
};