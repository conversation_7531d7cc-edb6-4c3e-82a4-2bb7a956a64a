import React, { useState } from 'react';
import {
  Card,
  Upload,
  Button,
  Space,
  Progress,
  Alert,
  Statistic,
  Row,
  Col,
  Modal,
  Select,
  Input,
  message,
} from 'antd';
import {
  UploadOutlined,
  DownloadOutlined,
  ClearOutlined,
  FileTextOutlined,
  ExclamationCircleOutlined,
  ExperimentOutlined,
} from '@ant-design/icons';
import type { UploadProps, UploadFile } from 'antd/es/upload';
import { useAppStore } from '@/stores/useAppStore';
import { ExportUtils } from '@/utils/exportUtils';
import { ExportOptions } from '@/types';
import { downloadDemoLogFile, generateDemoLogContent } from '@/utils/demoData';

const { Option } = Select;
const { confirm } = Modal;

interface FileUploadProps {
  className?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({ className }) => {
  const {
    records,
    uploadStatus,
    loading,
    error,
    parseLogFile,
    clearData,
    updateUploadStatus,
  } = useAppStore();

  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'excel',
    filename: ExportUtils.generateFilename(),
    includeCharts: false,
  });

  // 文件上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    fileList,
    accept: '.log,.txt',
    beforeUpload: file => {
      // 检查文件类型
      const isValidType =
        file.type === 'text/plain' ||
        file.name.endsWith('.log') ||
        file.name.endsWith('.txt');

      if (!isValidType) {
        message.error('只支持 .log 和 .txt 格式的文件');
        return false;
      }

      // 检查文件大小（限制为50MB）
      const isValidSize = file.size / 1024 / 1024 < 50;
      if (!isValidSize) {
        message.error('文件大小不能超过50MB');
        return false;
      }

      return false; // 阻止自动上传
    },
    onChange: info => {
      setFileList(info.fileList.slice(-1)); // 只保留最新的文件
    },
    onRemove: () => {
      setFileList([]);
    },
  };

  // 处理文件解析
  const handleParseFile = async () => {
    if (fileList.length === 0) {
      message.warning('请先选择文件');
      return;
    }

    const file = fileList[0].originFileObj;
    if (!file) {
      message.error('文件读取失败');
      return;
    }

    updateUploadStatus({ loading: true, progress: 0, error: null });

    try {
      // 读取文件内容
      const content = await readFileContent(file);

      // 模拟进度更新
      updateUploadStatus({ progress: 30 });

      // 解析文件
      const result = await parseLogFile(content);

      updateUploadStatus({ progress: 100 });

      if (result.success) {
        message.success(`解析成功！共解析 ${result.parsedRecords} 条记录`);
        updateUploadStatus({
          loading: false,
          success: true,
          error: null,
        });
      } else {
        message.error(`解析失败：${result.errors.join('; ')}`);
        updateUploadStatus({
          loading: false,
          success: false,
          error: result.errors.join('; '),
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      message.error(`文件处理失败：${errorMessage}`);
      updateUploadStatus({
        loading: false,
        success: false,
        error: errorMessage,
      });
    }
  };

  // 读取文件内容
  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = e => {
        const content = e.target?.result as string;
        resolve(content);
      };
      reader.onerror = () => {
        reject(new Error('文件读取失败'));
      };
      reader.readAsText(file, 'utf-8');
    });
  };

  // 处理数据导出
  const handleExport = () => {
    if (records.length === 0) {
      message.warning('没有可导出的数据');
      return;
    }

    setExportModalVisible(true);
  };

  // 确认导出
  const confirmExport = async () => {
    try {
      const validation = ExportUtils.validateExportData(
        records,
        exportOptions.selectedRecords
      );
      if (!validation.valid) {
        message.error(validation.message);
        return;
      }

      ExportUtils.exportData(records, exportOptions);
      message.success('导出成功！');
      setExportModalVisible(false);
    } catch (error) {
      message.error(error instanceof Error ? error.message : '导出失败');
    }
  };

  // 清空数据
  const handleClearData = () => {
    confirm({
      title: '确认清空数据',
      icon: <ExclamationCircleOutlined />,
      content: '此操作将清空所有已解析的数据，是否继续？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        clearData();
        setFileList([]);
        message.success('数据已清空');
      },
    });
  };

  // 加载演示数据
  const handleLoadDemoData = async () => {
    try {
      const demoContent = generateDemoLogContent(30);
      const result = await parseLogFile(demoContent);

      if (result.success) {
        message.success(`演示数据加载成功！共 ${result.parsedRecords} 条记录`);
      } else {
        message.error('演示数据加载失败');
      }
    } catch (error) {
      message.error('演示数据生成失败');
    }
  };

  // 下载演示文件
  const handleDownloadDemo = () => {
    downloadDemoLogFile(50);
    message.success('演示文件下载成功！');
  };

  return (
    <Card title="文件管理" className={`fade-in ${className || ''}`}>
      <Row gutter={[16, 16]}>
        {/* 文件上传区域 */}
        <Col span={24}>
          <Card size="small" title="文件上传">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Upload.Dragger {...uploadProps} style={{ padding: '20px' }}>
                <p className="ant-upload-drag-icon">
                  <FileTextOutlined
                    style={{ fontSize: '48px', color: '#1890ff' }}
                  />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">
                  支持 .log 和 .txt 格式的日志文件，文件大小限制50MB
                </p>
              </Upload.Dragger>

              <Space wrap>
                <Button
                  type="primary"
                  icon={<UploadOutlined />}
                  onClick={handleParseFile}
                  loading={uploadStatus.loading}
                  disabled={fileList.length === 0}
                >
                  解析文件
                </Button>
                <Button
                  icon={<ExperimentOutlined />}
                  onClick={handleLoadDemoData}
                  loading={uploadStatus.loading}
                >
                  加载演示数据
                </Button>
                <Button
                  icon={<FileTextOutlined />}
                  onClick={handleDownloadDemo}
                >
                  下载演示文件
                </Button>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExport}
                  disabled={records.length === 0}
                >
                  导出数据
                </Button>
                <Button
                  icon={<ClearOutlined />}
                  onClick={handleClearData}
                  disabled={records.length === 0}
                  danger
                >
                  清空数据
                </Button>
              </Space>
            </Space>
          </Card>
        </Col>

        {/* 上传进度 */}
        {uploadStatus.loading && (
          <Col span={24}>
            <Alert
              message="正在处理文件..."
              description={
                <Progress
                  percent={uploadStatus.progress}
                  status="active"
                  strokeColor="#1890ff"
                />
              }
              type="info"
              showIcon
            />
          </Col>
        )}

        {/* 错误信息 */}
        {error && (
          <Col span={24}>
            <Alert
              message="处理失败"
              description={error}
              type="error"
              showIcon
              closable
            />
          </Col>
        )}

        {/* 成功信息和统计 */}
        {uploadStatus.success && records.length > 0 && (
          <Col span={24}>
            <Alert
              message="文件解析成功"
              description="数据已成功加载，可以进行分析和导出操作"
              type="success"
              showIcon
            />
          </Col>
        )}

        {/* 数据统计 */}
        {records.length > 0 && (
          <Col span={24}>
            <Card size="small" title="数据统计">
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic
                    title="总记录数"
                    value={records.length}
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="STU1记录"
                    value={records.filter(r => r.stageNum === 'STU1').length}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="STU2记录"
                    value={records.filter(r => r.stageNum === 'STU2').length}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="数据完整性"
                    value={100}
                    suffix="%"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        )}
      </Row>

      {/* 导出配置模态框 */}
      <Modal
        title="导出数据"
        open={exportModalVisible}
        onOk={confirmExport}
        onCancel={() => setExportModalVisible(false)}
        okText="导出"
        cancelText="取消"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <label>导出格式：</label>
            <Select
              value={exportOptions.format}
              style={{ width: '100%', marginTop: 8 }}
              onChange={format =>
                setExportOptions(prev => ({ ...prev, format }))
              }
            >
              <Option value="excel">Excel (.xlsx)</Option>
              <Option value="csv">CSV (.csv)</Option>
            </Select>
          </div>

          <div>
            <label>文件名：</label>
            <Input
              value={exportOptions.filename}
              style={{ marginTop: 8 }}
              onChange={e =>
                setExportOptions(prev => ({
                  ...prev,
                  filename: e.target.value,
                }))
              }
              placeholder="请输入文件名"
            />
          </div>

          <Alert
            message="导出说明"
            description={`将导出 ${records.length} 条记录，包含所有坐标点、计算参数和统计信息。`}
            type="info"
            showIcon
          />
        </Space>
      </Modal>
    </Card>
  );
};

export default FileUpload;
