{"name": "@ant-design/fast-color", "version": "2.0.6", "description": "fast and small color class", "keywords": ["react", "react-component", "react-trigger", "trigger"], "homepage": "https://github.com/ant-design/fast-color", "bugs": {"url": "https://github.com/ant-design/fast-color/issues"}, "repository": {"type": "git", "url": "https://github.com/ant-design/fast-color.git"}, "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://guoyunhe.me/"}, "main": "./lib/index", "module": "./es/index", "files": ["es", "lib"], "scripts": {"bench": "vitest bench", "build": "dumi build", "compile": "father build", "coverage": "rc-test --coverage", "lint": "eslint src/ tests/ --ext .tsx,.ts,.jsx,.js", "now-build": "npm run build", "prepublishOnly": "npm run compile", "start": "dumi dev", "test": "rc-test"}, "dependencies": {"@babel/runtime": "^7.24.7"}, "devDependencies": {"@ctrl/tinycolor": "^4.1.0", "@rc-component/father-plugin": "^1.0.3", "@types/jest": "^29.5.12", "@types/node": "^22.1.0", "@umijs/fabric": "^4.0.1", "color2k": "^2.0.3", "cross-env": "^7.0.3", "dumi": "^2.3.8", "eslint": "^8.57.0", "father": "^4.4.4", "np": "^10.0.5", "rc-test": "^7.0.15", "typescript": "^5.4.5", "vitest": "^1.6.0"}, "engines": {"node": ">=8.x"}}