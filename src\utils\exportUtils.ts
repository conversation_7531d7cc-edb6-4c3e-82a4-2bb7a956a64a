import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import <PERSON> from 'papapar<PERSON>';
import { LogRecord, ExportOptions } from '@/types';
import { MathUtils } from './mathUtils';

/**
 * 数据导出工具类
 */
export class ExportUtils {
  /**
   * 导出数据为CSV格式
   * @param records 记录数组
   * @param filename 文件名
   * @param selectedRecords 选中的记录ID数组
   */
  static exportToCSV(
    records: LogRecord[],
    filename: string = 'device_log_data',
    selectedRecords?: string[]
  ): void {
    // 过滤选中的记录
    const dataToExport = selectedRecords
      ? records.filter(record => selectedRecords.includes(record.id))
      : records;

    if (dataToExport.length === 0) {
      throw new Error('没有可导出的数据');
    }

    // 转换数据格式
    const csvData = dataToExport.map((record, index) => ({
      序号: index + 1,
      时间戳: record.timestamp,
      机台号: record.stageNum,
      D1_X: MathUtils.formatNumber(record.d1.x),
      D1_Y: MathUtils.formatNumber(record.d1.y),
      D2_X: MathUtils.formatNumber(record.d2.x),
      D2_Y: MathUtils.formatNumber(record.d2.y),
      D3_X: MathUtils.formatNumber(record.d3.x),
      D3_Y: MathUtils.formatNumber(record.d3.y),
      Ra: MathUtils.formatNumber(record.ra),
      D4_X: MathUtils.formatNumber(record.d4.x),
      D4_Y: MathUtils.formatNumber(record.d4.y),
      D5_X: MathUtils.formatNumber(record.d5.x),
      D5_Y: MathUtils.formatNumber(record.d5.y),
      Rb: MathUtils.formatNumber(record.rb),
      D6_X: MathUtils.formatNumber(record.d6.x),
      D6_Y: MathUtils.formatNumber(record.d6.y),
      m1: MathUtils.formatNumber(record.m1),
      m2: MathUtils.formatNumber(record.m2, 6),
      m3: MathUtils.formatNumber(record.m3),
      tanValue1: MathUtils.formatNumber(record.tanValue1),
      tanValue2: MathUtils.formatNumber(record.tanValue2),
      tanSpec: MathUtils.formatNumber(record.tanSpec),
      '面积(mm²)': MathUtils.formatNumber(record.area || 0),
    }));

    // 生成CSV内容
    const csv = Papa.unparse(csvData, {
      header: true,
      encoding: 'utf-8',
    });

    // 添加BOM以支持中文
    const csvWithBOM = '\uFEFF' + csv;

    // 创建Blob并下载
    const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, `${filename}.csv`);
  }

  /**
   * 导出数据为Excel格式
   * @param records 记录数组
   * @param filename 文件名
   * @param selectedRecords 选中的记录ID数组
   * @param includeStatistics 是否包含统计信息
   */
  static exportToExcel(
    records: LogRecord[],
    filename: string = 'device_log_data',
    selectedRecords?: string[],
    includeStatistics: boolean = true
  ): void {
    // 过滤选中的记录
    const dataToExport = selectedRecords
      ? records.filter(record => selectedRecords.includes(record.id))
      : records;

    if (dataToExport.length === 0) {
      throw new Error('没有可导出的数据');
    }

    // 创建工作簿
    const workbook = XLSX.utils.book_new();

    // 主数据工作表
    const mainData = dataToExport.map((record, index) => ({
      序号: index + 1,
      时间戳: record.timestamp,
      机台号: record.stageNum,
      D1_X: record.d1.x,
      D1_Y: record.d1.y,
      D2_X: record.d2.x,
      D2_Y: record.d2.y,
      D3_X: record.d3.x,
      D3_Y: record.d3.y,
      Ra: record.ra,
      D4_X: record.d4.x,
      D4_Y: record.d4.y,
      D5_X: record.d5.x,
      D5_Y: record.d5.y,
      Rb: record.rb,
      D6_X: record.d6.x,
      D6_Y: record.d6.y,
      m1: record.m1,
      m2: record.m2,
      m3: record.m3,
      tanValue1: record.tanValue1,
      tanValue2: record.tanValue2,
      tanSpec: record.tanSpec,
      '面积(mm²)': record.area || 0,
    }));

    const mainWorksheet = XLSX.utils.json_to_sheet(mainData);
    XLSX.utils.book_append_sheet(workbook, mainWorksheet, '主数据');

    // 统计信息工作表
    if (includeStatistics) {
      const stu1Records = dataToExport.filter(r => r.stageNum === 'STU1');
      const stu2Records = dataToExport.filter(r => r.stageNum === 'STU2');

      const stu1TanValue1 = stu1Records.map(r => r.tanValue1);
      const stu1TanValue2 = stu1Records.map(r => r.tanValue2);
      const stu2TanValue1 = stu2Records.map(r => r.tanValue1);
      const stu2TanValue2 = stu2Records.map(r => r.tanValue2);
      const areas = dataToExport.map(r => r.area || 0);

      const statisticsData = [
        { 统计项目: '总记录数', 数值: dataToExport.length },
        { 统计项目: 'STU1记录数', 数值: stu1Records.length },
        { 统计项目: 'STU2记录数', 数值: stu2Records.length },
        { 统计项目: '', 数值: '' },
        {
          统计项目: 'STU1 tanValue1 均值',
          数值: MathUtils.calculateMean(stu1TanValue1),
        },
        {
          统计项目: 'STU1 tanValue1 标准差',
          数值: MathUtils.calculateStandardDeviation(stu1TanValue1),
        },
        {
          统计项目: 'STU1 tanValue2 均值',
          数值: MathUtils.calculateMean(stu1TanValue2),
        },
        {
          统计项目: 'STU1 tanValue2 标准差',
          数值: MathUtils.calculateStandardDeviation(stu1TanValue2),
        },
        { 统计项目: '', 数值: '' },
        {
          统计项目: 'STU2 tanValue1 均值',
          数值: MathUtils.calculateMean(stu2TanValue1),
        },
        {
          统计项目: 'STU2 tanValue1 标准差',
          数值: MathUtils.calculateStandardDeviation(stu2TanValue1),
        },
        {
          统计项目: 'STU2 tanValue2 均值',
          数值: MathUtils.calculateMean(stu2TanValue2),
        },
        {
          统计项目: 'STU2 tanValue2 标准差',
          数值: MathUtils.calculateStandardDeviation(stu2TanValue2),
        },
        { 统计项目: '', 数值: '' },
        { 统计项目: '面积均值 (mm²)', 数值: MathUtils.calculateMean(areas) },
        {
          统计项目: '面积标准差 (mm²)',
          数值: MathUtils.calculateStandardDeviation(areas),
        },
        { 统计项目: '面积最小值 (mm²)', 数值: Math.min(...areas) },
        { 统计项目: '面积最大值 (mm²)', 数值: Math.max(...areas) },
      ];

      const statisticsWorksheet = XLSX.utils.json_to_sheet(statisticsData);
      XLSX.utils.book_append_sheet(workbook, statisticsWorksheet, '统计信息');
    }

    // STU1数据工作表
    const stu1Data = dataToExport
      .filter(r => r.stageNum === 'STU1')
      .map((record, index) => ({
        序号: index + 1,
        时间戳: record.timestamp,
        tanValue1: record.tanValue1,
        tanValue2: record.tanValue2,
        '面积(mm²)': record.area || 0,
        m1: record.m1,
        m2: record.m2,
        m3: record.m3,
      }));

    if (stu1Data.length > 0) {
      const stu1Worksheet = XLSX.utils.json_to_sheet(stu1Data);
      XLSX.utils.book_append_sheet(workbook, stu1Worksheet, 'STU1数据');
    }

    // STU2数据工作表
    const stu2Data = dataToExport
      .filter(r => r.stageNum === 'STU2')
      .map((record, index) => ({
        序号: index + 1,
        时间戳: record.timestamp,
        tanValue1: record.tanValue1,
        tanValue2: record.tanValue2,
        '面积(mm²)': record.area || 0,
        m1: record.m1,
        m2: record.m2,
        m3: record.m3,
      }));

    if (stu2Data.length > 0) {
      const stu2Worksheet = XLSX.utils.json_to_sheet(stu2Data);
      XLSX.utils.book_append_sheet(workbook, stu2Worksheet, 'STU2数据');
    }

    // 生成Excel文件
    XLSX.writeFile(workbook, `${filename}.xlsx`);
  }

  /**
   * 根据选项导出数据
   * @param records 记录数组
   * @param options 导出选项
   */
  static exportData(records: LogRecord[], options: ExportOptions): void {
    const { format, filename, selectedRecords } = options;

    try {
      if (format === 'csv') {
        this.exportToCSV(records, filename, selectedRecords);
      } else if (format === 'excel') {
        this.exportToExcel(records, filename, selectedRecords, true);
      } else {
        throw new Error('不支持的导出格式');
      }
    } catch (error) {
      throw new Error(
        `导出失败: ${error instanceof Error ? error.message : '未知错误'}`
      );
    }
  }

  /**
   * 生成导出文件名
   * @param prefix 前缀
   * @param suffix 后缀
   * @returns 文件名
   */
  static generateFilename(
    prefix: string = 'device_log_data',
    suffix?: string
  ): string {
    const timestamp = new Date()
      .toISOString()
      .slice(0, 19)
      .replace(/[:-]/g, '');
    const parts = [prefix, timestamp];

    if (suffix) {
      parts.push(suffix);
    }

    return parts.join('_');
  }

  /**
   * 验证导出数据
   * @param records 记录数组
   * @param selectedRecords 选中的记录ID数组
   * @returns 验证结果
   */
  static validateExportData(
    records: LogRecord[],
    selectedRecords?: string[]
  ): { valid: boolean; message?: string } {
    if (!records || records.length === 0) {
      return { valid: false, message: '没有可导出的数据' };
    }

    if (selectedRecords && selectedRecords.length === 0) {
      return { valid: false, message: '没有选中任何记录' };
    }

    const dataToExport = selectedRecords
      ? records.filter(record => selectedRecords.includes(record.id))
      : records;

    if (dataToExport.length === 0) {
      return { valid: false, message: '选中的记录不存在' };
    }

    return { valid: true };
  }
}
