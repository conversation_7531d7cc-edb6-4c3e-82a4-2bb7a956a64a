"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.UnitDependencies = void 0;
var _dependenciesBigNumberClassGenerated = require("./dependenciesBigNumberClass.generated.js");
var _dependenciesComplexClassGenerated = require("./dependenciesComplexClass.generated.js");
var _dependenciesFractionClassGenerated = require("./dependenciesFractionClass.generated.js");
var _dependenciesAbsGenerated = require("./dependenciesAbs.generated.js");
var _dependenciesAddScalarGenerated = require("./dependenciesAddScalar.generated.js");
var _dependenciesDivideScalarGenerated = require("./dependenciesDivideScalar.generated.js");
var _dependenciesEqualGenerated = require("./dependenciesEqual.generated.js");
var _dependenciesFixGenerated = require("./dependenciesFix.generated.js");
var _dependenciesFormatGenerated = require("./dependenciesFormat.generated.js");
var _dependenciesIsNumericGenerated = require("./dependenciesIsNumeric.generated.js");
var _dependenciesMultiplyScalarGenerated = require("./dependenciesMultiplyScalar.generated.js");
var _dependenciesNumberGenerated = require("./dependenciesNumber.generated.js");
var _dependenciesPowGenerated = require("./dependenciesPow.generated.js");
var _dependenciesRoundGenerated = require("./dependenciesRound.generated.js");
var _dependenciesSubtractScalarGenerated = require("./dependenciesSubtractScalar.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var UnitDependencies = exports.UnitDependencies = {
  BigNumberDependencies: _dependenciesBigNumberClassGenerated.BigNumberDependencies,
  ComplexDependencies: _dependenciesComplexClassGenerated.ComplexDependencies,
  FractionDependencies: _dependenciesFractionClassGenerated.FractionDependencies,
  absDependencies: _dependenciesAbsGenerated.absDependencies,
  addScalarDependencies: _dependenciesAddScalarGenerated.addScalarDependencies,
  divideScalarDependencies: _dependenciesDivideScalarGenerated.divideScalarDependencies,
  equalDependencies: _dependenciesEqualGenerated.equalDependencies,
  fixDependencies: _dependenciesFixGenerated.fixDependencies,
  formatDependencies: _dependenciesFormatGenerated.formatDependencies,
  isNumericDependencies: _dependenciesIsNumericGenerated.isNumericDependencies,
  multiplyScalarDependencies: _dependenciesMultiplyScalarGenerated.multiplyScalarDependencies,
  numberDependencies: _dependenciesNumberGenerated.numberDependencies,
  powDependencies: _dependenciesPowGenerated.powDependencies,
  roundDependencies: _dependenciesRoundGenerated.roundDependencies,
  subtractScalarDependencies: _dependenciesSubtractScalarGenerated.subtractScalarDependencies,
  createUnitClass: _factoriesAny.createUnitClass
};