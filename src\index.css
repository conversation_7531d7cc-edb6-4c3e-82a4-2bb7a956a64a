@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family:
    'Inter',
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Roboto',
    'Oxygen',
    'Ubuntu',
    'Cantarell',
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Ant Design 样式覆盖 */
.ant-layout {
  background: #f8fafc !important;
}

.ant-card {
  border-radius: 12px !important;
  box-shadow:
    0 1px 3px 0 rgb(0 0 0 / 0.1),
    0 1px 2px -1px rgb(0 0 0 / 0.1) !important;
}

.ant-table-thead > tr > th {
  background: #f1f5f9 !important;
  font-weight: 600 !important;
}

.ant-btn-primary {
  background: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

.ant-btn-primary:hover {
  background: #2563eb !important;
  border-color: #2563eb !important;
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
