import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { LogRecord, StatisticsData, UploadStatus, ParseResult } from '@/types';
import { LogParser } from '@/utils/logParser';
import { MathUtils } from '@/utils/mathUtils';

interface AppState {
  // 数据状态
  records: LogRecord[];
  selectedRecord: LogRecord | null;
  statistics: StatisticsData | null;

  // UI状态
  uploadStatus: UploadStatus;
  loading: boolean;
  error: string | null;

  // 操作方法
  setRecords: (records: LogRecord[]) => void;
  setSelectedRecord: (record: LogRecord | null) => void;
  parseLogFile: (content: string) => Promise<ParseResult>;
  calculateStatistics: () => void;
  clearData: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  updateUploadStatus: (status: Partial<UploadStatus>) => void;
}

const initialUploadStatus: UploadStatus = {
  loading: false,
  progress: 0,
  error: null,
  success: false,
};

export const useAppStore = create<AppState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      records: [],
      selectedRecord: null,
      statistics: null,
      uploadStatus: initialUploadStatus,
      loading: false,
      error: null,

      // 设置记录
      setRecords: (records: LogRecord[]) => {
        // 计算每条记录的面积
        const recordsWithArea = records.map(record => ({
          ...record,
          area: MathUtils.calculateTrajectoryArea(
            record.d1,
            record.d2,
            record.d3,
            record.d4,
            record.d5,
            record.d6
          ),
        }));

        set({ records: recordsWithArea, error: null });

        // 自动计算统计数据
        get().calculateStatistics();
      },

      // 设置选中记录
      setSelectedRecord: (record: LogRecord | null) => {
        set({ selectedRecord: record });
      },

      // 解析日志文件
      parseLogFile: async (content: string): Promise<ParseResult> => {
        set({ loading: true, error: null });

        try {
          // 模拟异步处理
          await new Promise(resolve => setTimeout(resolve, 100));

          const result = LogParser.parseLogContent(content);

          if (result.success) {
            get().setRecords(result.records);
            set({
              uploadStatus: {
                ...initialUploadStatus,
                success: true,
              },
            });
          } else {
            set({
              error: `解析失败: ${result.errors.join('; ')}`,
              uploadStatus: {
                ...initialUploadStatus,
                error: result.errors.join('; '),
              },
            });
          }

          return result;
        } catch (error) {
          const errorMessage =
            error instanceof Error ? error.message : '未知错误';
          set({
            error: `解析过程中发生错误: ${errorMessage}`,
            uploadStatus: {
              ...initialUploadStatus,
              error: errorMessage,
            },
          });

          return {
            success: false,
            records: [],
            errors: [errorMessage],
            totalLines: 0,
            parsedRecords: 0,
          };
        } finally {
          set({ loading: false });
        }
      },

      // 计算统计数据
      calculateStatistics: () => {
        const { records } = get();

        if (records.length === 0) {
          set({ statistics: null });
          return;
        }

        // 按机台分组数据
        const stu1Records = records.filter(r => r.stageNum === 'STU1');
        const stu2Records = records.filter(r => r.stageNum === 'STU2');

        const statistics: StatisticsData = {
          stu1TanValue1: stu1Records.map(r => r.tanValue1),
          stu1TanValue2: stu1Records.map(r => r.tanValue2),
          stu2TanValue1: stu2Records.map(r => r.tanValue1),
          stu2TanValue2: stu2Records.map(r => r.tanValue2),
          areas: records.map(r => r.area || 0),
        };

        set({ statistics });
      },

      // 清空数据
      clearData: () => {
        set({
          records: [],
          selectedRecord: null,
          statistics: null,
          uploadStatus: initialUploadStatus,
          error: null,
        });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },

      // 设置错误信息
      setError: (error: string | null) => {
        set({ error });
      },

      // 更新上传状态
      updateUploadStatus: (status: Partial<UploadStatus>) => {
        set(state => ({
          uploadStatus: { ...state.uploadStatus, ...status },
        }));
      },
    }),
    {
      name: 'app-store',
    }
  )
);
