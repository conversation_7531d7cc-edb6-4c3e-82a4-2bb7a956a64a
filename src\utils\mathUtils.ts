import { Point, NormalDistribution } from '@/types';

/**
 * 数学计算工具类
 */
export class MathUtils {
  /**
   * 计算多边形面积（使用鞋带公式）
   * @param points 顶点数组
   * @returns 面积（平方毫米）
   */
  static calculatePolygonArea(points: Point[]): number {
    if (points.length < 3) {
      return 0;
    }

    let area = 0;
    const n = points.length;

    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      area += points[i].x * points[j].y;
      area -= points[j].x * points[i].y;
    }

    return Math.abs(area) / 2;
  }

  /**
   * 计算六点连线封闭图形面积
   * @param d1 第一个点
   * @param d2 第二个点
   * @param d3 第三个点
   * @param d4 第四个点
   * @param d5 第五个点
   * @param d6 第六个点
   * @returns 面积（平方毫米）
   */
  static calculateTrajectoryArea(
    d1: Point,
    d2: Point,
    d3: Point,
    d4: Point,
    d5: Point,
    d6: Point
  ): number {
    const points = [d1, d2, d3, d4, d5, d6];
    return this.calculatePolygonArea(points);
  }

  /**
   * 计算数组的平均值
   * @param values 数值数组
   * @returns 平均值
   */
  static calculateMean(values: number[]): number {
    if (values.length === 0) return 0;
    return values.reduce((sum, value) => sum + value, 0) / values.length;
  }

  /**
   * 计算数组的标准差
   * @param values 数值数组
   * @returns 标准差
   */
  static calculateStandardDeviation(values: number[]): number {
    if (values.length === 0) return 0;

    const mean = this.calculateMean(values);
    const squaredDifferences = values.map(value => Math.pow(value - mean, 2));
    const variance = this.calculateMean(squaredDifferences);

    return Math.sqrt(variance);
  }

  /**
   * 计算正态分布参数
   * @param values 数值数组
   * @returns 正态分布参数
   */
  static calculateNormalDistribution(values: number[]): NormalDistribution {
    const mean = this.calculateMean(values);
    const stdDev = this.calculateStandardDeviation(values);

    return {
      mean,
      stdDev,
      data: values,
    };
  }

  /**
   * 生成正态分布概率密度函数值
   * @param x 输入值
   * @param mean 均值
   * @param stdDev 标准差
   * @returns 概率密度值
   */
  static normalPDF(x: number, mean: number, stdDev: number): number {
    if (stdDev === 0) return 0;

    const coefficient = 1 / (stdDev * Math.sqrt(2 * Math.PI));
    const exponent = -0.5 * Math.pow((x - mean) / stdDev, 2);

    return coefficient * Math.exp(exponent);
  }

  /**
   * 生成正态分布曲线数据点
   * @param mean 均值
   * @param stdDev 标准差
   * @param points 数据点数量
   * @param range 范围倍数（标准差的倍数）
   * @returns 曲线数据点
   */
  static generateNormalCurve(
    mean: number,
    stdDev: number,
    points: number = 100,
    range: number = 3
  ): { x: number; y: number }[] {
    if (stdDev === 0) return [];

    const start = mean - range * stdDev;
    const end = mean + range * stdDev;
    const step = (end - start) / (points - 1);

    const curve: { x: number; y: number }[] = [];

    for (let i = 0; i < points; i++) {
      const x = start + i * step;
      const y = this.normalPDF(x, mean, stdDev);
      curve.push({ x, y });
    }

    return curve;
  }

  /**
   * 创建直方图数据
   * @param values 数值数组
   * @param bins 分组数量
   * @returns 直方图数据
   */
  static createHistogram(
    values: number[],
    bins: number = 20
  ): {
    bins: { min: number; max: number; count: number; center: number }[];
    min: number;
    max: number;
  } {
    if (values.length === 0) {
      return { bins: [], min: 0, max: 0 };
    }

    const min = Math.min(...values);
    const max = Math.max(...values);
    const binWidth = (max - min) / bins;

    const binData = Array.from({ length: bins }, (_, i) => ({
      min: min + i * binWidth,
      max: min + (i + 1) * binWidth,
      count: 0,
      center: min + (i + 0.5) * binWidth,
    }));

    // 统计每个区间的数据点数量
    values.forEach(value => {
      let binIndex = Math.floor((value - min) / binWidth);
      // 处理边界情况
      if (binIndex >= bins) binIndex = bins - 1;
      if (binIndex < 0) binIndex = 0;
      binData[binIndex].count++;
    });

    return {
      bins: binData,
      min,
      max,
    };
  }

  /**
   * 计算两点之间的距离
   * @param p1 第一个点
   * @param p2 第二个点
   * @returns 距离
   */
  static calculateDistance(p1: Point, p2: Point): number {
    return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
  }

  /**
   * 计算轨迹总长度
   * @param points 轨迹点数组
   * @returns 总长度
   */
  static calculateTrajectoryLength(points: Point[]): number {
    if (points.length < 2) return 0;

    let totalLength = 0;
    for (let i = 0; i < points.length - 1; i++) {
      totalLength += this.calculateDistance(points[i], points[i + 1]);
    }

    // 加上最后一点到第一点的距离（封闭图形）
    if (points.length > 2) {
      totalLength += this.calculateDistance(
        points[points.length - 1],
        points[0]
      );
    }

    return totalLength;
  }

  /**
   * 格式化数值显示
   * @param value 数值
   * @param decimals 小数位数
   * @returns 格式化后的字符串
   */
  static formatNumber(value: number, decimals: number = 2): string {
    return value.toFixed(decimals);
  }

  /**
   * 检查数值是否有效
   * @param value 数值
   * @returns 是否有效
   */
  static isValidNumber(value: any): value is number {
    return typeof value === 'number' && !isNaN(value) && isFinite(value);
  }
}
