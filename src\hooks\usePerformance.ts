import { useEffect, useRef, useState } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage?: number;
  componentMountTime: number;
}

/**
 * 性能监控Hook
 * @param componentName 组件名称
 * @returns 性能指标
 */
export const usePerformance = (componentName: string): PerformanceMetrics => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    componentMountTime: 0,
  });

  const mountTimeRef = useRef<number>(Date.now());
  const renderStartRef = useRef<number>(Date.now());

  useEffect(() => {
    // 组件挂载时间
    const mountTime = Date.now() - mountTimeRef.current;

    // 获取内存使用情况（如果浏览器支持）
    const getMemoryUsage = (): number | undefined => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        return memory.usedJSHeapSize / 1024 / 1024; // 转换为MB
      }
      return undefined;
    };

    setMetrics(prev => ({
      ...prev,
      componentMountTime: mountTime,
      memoryUsage: getMemoryUsage(),
    }));

    // 在开发环境下输出性能信息
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${componentName} mounted in ${mountTime}ms`);

      const memoryUsage = getMemoryUsage();
      if (memoryUsage) {
        console.log(
          `[Performance] ${componentName} memory usage: ${memoryUsage.toFixed(2)}MB`
        );
      }
    }
  }, [componentName]);

  useEffect(() => {
    // 测量渲染时间
    const renderTime = Date.now() - renderStartRef.current;

    setMetrics(prev => ({
      ...prev,
      renderTime,
    }));

    // 重置渲染开始时间
    renderStartRef.current = Date.now();
  });

  return metrics;
};

/**
 * 性能计时器Hook
 * @param label 计时器标签
 * @returns 开始和结束计时的函数
 */
export const usePerformanceTimer = (label: string) => {
  const startTimeRef = useRef<number>(0);

  const start = () => {
    startTimeRef.current = performance.now();
    if (process.env.NODE_ENV === 'development') {
      console.time(label);
    }
  };

  const end = () => {
    const duration = performance.now() - startTimeRef.current;

    if (process.env.NODE_ENV === 'development') {
      console.timeEnd(label);
      console.log(`[Performance Timer] ${label}: ${duration.toFixed(2)}ms`);
    }

    return duration;
  };

  return { start, end };
};

/**
 * 长任务监控Hook
 * @param threshold 长任务阈值（毫秒）
 * @param onLongTask 长任务回调
 */
export const useLongTaskMonitor = (
  threshold: number = 50,
  onLongTask?: (duration: number) => void
) => {
  useEffect(() => {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    const observer = new PerformanceObserver(list => {
      const entries = list.getEntries();

      entries.forEach(entry => {
        if (entry.duration > threshold) {
          if (process.env.NODE_ENV === 'development') {
            console.warn(
              `[Long Task] Task took ${entry.duration.toFixed(2)}ms`
            );
          }

          onLongTask?.(entry.duration);
        }
      });
    });

    try {
      observer.observe({ entryTypes: ['longtask'] });
    } catch (error) {
      // 某些浏览器可能不支持 longtask
      console.warn('Long task monitoring not supported');
    }

    return () => {
      observer.disconnect();
    };
  }, [threshold, onLongTask]);
};

/**
 * 内存使用监控Hook
 * @param interval 监控间隔（毫秒）
 * @returns 内存使用信息
 */
export const useMemoryMonitor = (interval: number = 5000) => {
  const [memoryInfo, setMemoryInfo] = useState<{
    used: number;
    total: number;
    limit: number;
  } | null>(null);

  useEffect(() => {
    if (typeof window === 'undefined' || !('memory' in performance)) {
      return;
    }

    const updateMemoryInfo = () => {
      const memory = (performance as any).memory;
      setMemoryInfo({
        used: memory.usedJSHeapSize / 1024 / 1024, // MB
        total: memory.totalJSHeapSize / 1024 / 1024, // MB
        limit: memory.jsHeapSizeLimit / 1024 / 1024, // MB
      });
    };

    updateMemoryInfo();
    const intervalId = setInterval(updateMemoryInfo, interval);

    return () => {
      clearInterval(intervalId);
    };
  }, [interval]);

  return memoryInfo;
};

/**
 * FPS监控Hook
 * @returns 当前FPS值
 */
export const useFPSMonitor = () => {
  const [fps, setFps] = useState<number>(0);
  const frameCountRef = useRef<number>(0);
  const lastTimeRef = useRef<number>(performance.now());
  const animationIdRef = useRef<number>(0);

  useEffect(() => {
    const updateFPS = () => {
      frameCountRef.current++;
      const currentTime = performance.now();

      if (currentTime - lastTimeRef.current >= 1000) {
        const currentFPS = Math.round(
          (frameCountRef.current * 1000) / (currentTime - lastTimeRef.current)
        );

        setFps(currentFPS);
        frameCountRef.current = 0;
        lastTimeRef.current = currentTime;
      }

      animationIdRef.current = requestAnimationFrame(updateFPS);
    };

    animationIdRef.current = requestAnimationFrame(updateFPS);

    return () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }
    };
  }, []);

  return fps;
};
