import { LogParser } from '../logParser';

// 测试用的日志数据
const testLogContent = `
[2025-08-05 00:05:18.89] [000] [8-DEBUG] STU1   (MCtrlStage.cpp, 3558)
[2025-08-05 00:05:18.89] [000] [8-DEBUG] G00 X805.081514 Y725.654069 Z16.000000   (MCtrlStage.cpp, 3643)
[2025-08-05 00:05:18.89] [000] [8-DEBUG] G01 X805.340156 Y1029.288522 Z16.000000   (MCtrlStage.cpp, 3656)
[2025-08-05 00:05:18.89] [000] [8-DEBUG] G03 X802.940619 Y1031.688985 R2.400000   (MCtrlStage.cpp, 3626)
[2025-08-05 00:05:18.89] [000] [8-DEBUG] G01 X273.016119 Y1031.791144 Z16.000000   (MCtrlStage.cpp, 3656)
[2025-08-05 00:05:18.89] [000] [8-DEBUG] G03 X270.615656 Y1029.391607 R2.400000   (MCtrlStage.cpp, 3626)
[2025-08-05 00:05:18.89] [000] [8-DEBUG] G01 X270.440156 Y706.315984 Z16.000000   (MCtrlStage.cpp, 3656)
[2025-08-05 00:05:18.89] [000] [8-DEBUG] m1 1173.953225, m2 -0.000193, m3 1840.881101, tanValue1 1517.355358, tanValue2 2853.580672, tanSpec 977.022835   (MCtrlStage.cpp, 3716)
[2025-08-05 00:05:18.89] [000] [8-DEBUG] ------------------------------------------------------------   (MCtrlStage.cpp, 3718)
[2025-08-05 00:05:36.60] [000] [8-DEBUG] STU2   (MCtrlStage.cpp, 3574)
[2025-08-05 00:05:36.60] [000] [8-DEBUG] G00 X1704.472969 YY723.159950 Z15.800000   (MCtrlStage.cpp, 3643)
[2025-08-05 00:05:36.60] [000] [8-DEBUG] G01 X1704.513729 YY1026.798780 Z15.800000   (MCtrlStage.cpp, 3656)
[2025-08-05 00:05:36.60] [000] [8-DEBUG] G03 X1702.113963 YY1029.198546 R2.400000   (MCtrlStage.cpp, 3626)
[2025-08-05 00:05:36.60] [000] [8-DEBUG] G01 X1172.223918 YY1029.147013 Z15.800000   (MCtrlStage.cpp, 3656)
[2025-08-05 00:05:36.60] [000] [8-DEBUG] G03 X1169.823684 YY1026.746780 R2.400000   (MCtrlStage.cpp, 3626)
[2025-08-05 00:05:36.60] [000] [8-DEBUG] G01 X1169.735956 YY703.635908 Z15.800000   (MCtrlStage.cpp, 3656)
[2025-08-05 00:05:36.60] [000] [8-DEBUG] m1 7449.464353, m2 0.000097, m3 3683.069315, tanValue1 4319.846207, tanValue2 2711.757302, tanSpec 977.022835   (MCtrlStage.cpp, 3716)
[2025-08-05 00:05:36.60] [000] [8-DEBUG] ------------------------------------------------------------   (MCtrlStage.cpp, 3718)
`;

describe('LogParser', () => {
  describe('parseLogContent', () => {
    it('should parse valid log content successfully', () => {
      const result = LogParser.parseLogContent(testLogContent);

      expect(result.success).toBe(true);
      expect(result.records).toHaveLength(2);
      expect(result.errors).toHaveLength(0);
      expect(result.parsedRecords).toBe(2);
    });

    it('should parse STU1 record correctly', () => {
      const result = LogParser.parseLogContent(testLogContent);
      const stu1Record = result.records.find(r => r.stageNum === 'STU1');

      expect(stu1Record).toBeDefined();
      expect(stu1Record!.stageNum).toBe('STU1');
      expect(stu1Record!.d1).toEqual({ x: 805.081514, y: 725.654069 });
      expect(stu1Record!.d2).toEqual({ x: 805.340156, y: 1029.288522 });
      expect(stu1Record!.d3).toEqual({ x: 802.940619, y: 1031.688985 });
      expect(stu1Record!.ra).toBe(2.4);
      expect(stu1Record!.d4).toEqual({ x: 273.016119, y: 1031.791144 });
      expect(stu1Record!.d5).toEqual({ x: 270.615656, y: 1029.391607 });
      expect(stu1Record!.rb).toBe(2.4);
      expect(stu1Record!.d6).toEqual({ x: 270.440156, y: 706.315984 });
      expect(stu1Record!.m1).toBe(1173.953225);
      expect(stu1Record!.m2).toBe(-0.000193);
      expect(stu1Record!.m3).toBe(1840.881101);
      expect(stu1Record!.tanValue1).toBe(1517.355358);
      expect(stu1Record!.tanValue2).toBe(2853.580672);
      expect(stu1Record!.tanSpec).toBe(977.022835);
    });

    it('should parse STU2 record correctly', () => {
      const result = LogParser.parseLogContent(testLogContent);
      const stu2Record = result.records.find(r => r.stageNum === 'STU2');

      expect(stu2Record).toBeDefined();
      expect(stu2Record!.stageNum).toBe('STU2');
      expect(stu2Record!.d1).toEqual({ x: 1704.472969, y: 723.15995 });
      expect(stu2Record!.d2).toEqual({ x: 1704.513729, y: 1026.79878 });
      expect(stu2Record!.d3).toEqual({ x: 1702.113963, y: 1029.198546 });
      expect(stu2Record!.ra).toBe(2.4);
      expect(stu2Record!.d4).toEqual({ x: 1172.223918, y: 1029.147013 });
      expect(stu2Record!.d5).toEqual({ x: 1169.823684, y: 1026.74678 });
      expect(stu2Record!.rb).toBe(2.4);
      expect(stu2Record!.d6).toEqual({ x: 1169.735956, y: 703.635908 });
      expect(stu2Record!.m1).toBe(7449.464353);
      expect(stu2Record!.m2).toBe(0.000097);
      expect(stu2Record!.m3).toBe(3683.069315);
      expect(stu2Record!.tanValue1).toBe(4319.846207);
      expect(stu2Record!.tanValue2).toBe(2711.757302);
      expect(stu2Record!.tanSpec).toBe(977.022835);
    });

    it('should handle empty content', () => {
      const result = LogParser.parseLogContent('');

      expect(result.success).toBe(true);
      expect(result.records).toHaveLength(0);
      expect(result.errors).toHaveLength(0);
      expect(result.parsedRecords).toBe(0);
    });

    it('should handle invalid content gracefully', () => {
      const invalidContent = 'This is not a valid log format';
      const result = LogParser.parseLogContent(invalidContent);

      expect(result.success).toBe(true);
      expect(result.records).toHaveLength(0);
      expect(result.parsedRecords).toBe(0);
    });
  });

  describe('validateRecords', () => {
    it('should validate correct records', () => {
      const result = LogParser.parseLogContent(testLogContent);
      const validation = LogParser.validateRecords(result.records);

      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should detect invalid records', () => {
      const invalidRecord = {
        id: 'test',
        timestamp: '2025-08-05 00:05:18.89',
        stageNum: 'INVALID' as any,
        d1: { x: NaN, y: 0 },
        d2: { x: 0, y: 0 },
        d3: { x: 0, y: 0 },
        ra: 0,
        d4: { x: 0, y: 0 },
        d5: { x: 0, y: 0 },
        rb: 0,
        d6: { x: 0, y: 0 },
        m1: 0,
        m2: 0,
        m3: 0,
        tanValue1: 0,
        tanValue2: 0,
        tanSpec: 0,
      };

      const validation = LogParser.validateRecords([invalidRecord]);

      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });
});

// 如果在Node.js环境中运行，导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testLogContent,
    LogParser,
  };
}
