"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.polynomialRootDependencies = void 0;
var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");
var _dependenciesCbrtGenerated = require("./dependenciesCbrt.generated.js");
var _dependenciesDivideGenerated = require("./dependenciesDivide.generated.js");
var _dependenciesEqualScalarGenerated = require("./dependenciesEqualScalar.generated.js");
var _dependenciesImGenerated = require("./dependenciesIm.generated.js");
var _dependenciesIsZeroGenerated = require("./dependenciesIsZero.generated.js");
var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");
var _dependenciesReGenerated = require("./dependenciesRe.generated.js");
var _dependenciesSqrtGenerated = require("./dependenciesSqrt.generated.js");
var _dependenciesSubtractGenerated = require("./dependenciesSubtract.generated.js");
var _dependenciesTypeOfGenerated = require("./dependenciesTypeOf.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _dependenciesUnaryMinusGenerated = require("./dependenciesUnaryMinus.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var polynomialRootDependencies = exports.polynomialRootDependencies = {
  addDependencies: _dependenciesAddGenerated.addDependencies,
  cbrtDependencies: _dependenciesCbrtGenerated.cbrtDependencies,
  divideDependencies: _dependenciesDivideGenerated.divideDependencies,
  equalScalarDependencies: _dependenciesEqualScalarGenerated.equalScalarDependencies,
  imDependencies: _dependenciesImGenerated.imDependencies,
  isZeroDependencies: _dependenciesIsZeroGenerated.isZeroDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  reDependencies: _dependenciesReGenerated.reDependencies,
  sqrtDependencies: _dependenciesSqrtGenerated.sqrtDependencies,
  subtractDependencies: _dependenciesSubtractGenerated.subtractDependencies,
  typeOfDependencies: _dependenciesTypeOfGenerated.typeOfDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  unaryMinusDependencies: _dependenciesUnaryMinusGenerated.unaryMinusDependencies,
  createPolynomialRoot: _factoriesAny.createPolynomialRoot
};