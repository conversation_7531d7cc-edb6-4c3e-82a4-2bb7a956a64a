"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.invDependencies = void 0;
var _dependenciesAbsGenerated = require("./dependenciesAbs.generated.js");
var _dependenciesAddScalarGenerated = require("./dependenciesAddScalar.generated.js");
var _dependenciesDetGenerated = require("./dependenciesDet.generated.js");
var _dependenciesDivideScalarGenerated = require("./dependenciesDivideScalar.generated.js");
var _dependenciesIdentityGenerated = require("./dependenciesIdentity.generated.js");
var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");
var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _dependenciesUnaryMinusGenerated = require("./dependenciesUnaryMinus.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var invDependencies = exports.invDependencies = {
  absDependencies: _dependenciesAbsGenerated.absDependencies,
  addScalarDependencies: _dependenciesAddScalarGenerated.addScalarDependencies,
  detDependencies: _dependenciesDetGenerated.detDependencies,
  divideScalarDependencies: _dependenciesDivideScalarGenerated.divideScalarDependencies,
  identityDependencies: _dependenciesIdentityGenerated.identityDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  unaryMinusDependencies: _dependenciesUnaryMinusGenerated.unaryMinusDependencies,
  createInv: _factoriesAny.createInv
};