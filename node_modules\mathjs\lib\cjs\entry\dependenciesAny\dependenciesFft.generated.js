"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.fftDependencies = void 0;
var _dependenciesAddScalarGenerated = require("./dependenciesAddScalar.generated.js");
var _dependenciesCeilGenerated = require("./dependenciesCeil.generated.js");
var _dependenciesConjGenerated = require("./dependenciesConj.generated.js");
var _dependenciesDivideScalarGenerated = require("./dependenciesDivideScalar.generated.js");
var _dependenciesDotDivideGenerated = require("./dependenciesDotDivide.generated.js");
var _dependenciesExpGenerated = require("./dependenciesExp.generated.js");
var _dependenciesIGenerated = require("./dependenciesI.generated.js");
var _dependenciesLog2Generated = require("./dependenciesLog2.generated.js");
var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");
var _dependenciesMultiplyScalarGenerated = require("./dependenciesMultiplyScalar.generated.js");
var _dependenciesPowGenerated = require("./dependenciesPow.generated.js");
var _dependenciesTauGenerated = require("./dependenciesTau.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var fftDependencies = exports.fftDependencies = {
  addScalarDependencies: _dependenciesAddScalarGenerated.addScalarDependencies,
  ceilDependencies: _dependenciesCeilGenerated.ceilDependencies,
  conjDependencies: _dependenciesConjGenerated.conjDependencies,
  divideScalarDependencies: _dependenciesDivideScalarGenerated.divideScalarDependencies,
  dotDivideDependencies: _dependenciesDotDivideGenerated.dotDivideDependencies,
  expDependencies: _dependenciesExpGenerated.expDependencies,
  iDependencies: _dependenciesIGenerated.iDependencies,
  log2Dependencies: _dependenciesLog2Generated.log2Dependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  multiplyScalarDependencies: _dependenciesMultiplyScalarGenerated.multiplyScalarDependencies,
  powDependencies: _dependenciesPowGenerated.powDependencies,
  tauDependencies: _dependenciesTauGenerated.tauDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createFft: _factoriesAny.createFft
};