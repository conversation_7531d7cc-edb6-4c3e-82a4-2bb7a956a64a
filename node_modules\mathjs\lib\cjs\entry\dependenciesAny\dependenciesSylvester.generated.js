"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.sylvesterDependencies = void 0;
var _dependenciesAbsGenerated = require("./dependenciesAbs.generated.js");
var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");
var _dependenciesConcatGenerated = require("./dependenciesConcat.generated.js");
var _dependenciesIdentityGenerated = require("./dependenciesIdentity.generated.js");
var _dependenciesIndexGenerated = require("./dependenciesIndex.generated.js");
var _dependenciesLusolveGenerated = require("./dependenciesLusolve.generated.js");
var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");
var _dependenciesMatrixFromColumnsGenerated = require("./dependenciesMatrixFromColumns.generated.js");
var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");
var _dependenciesRangeGenerated = require("./dependenciesRange.generated.js");
var _dependenciesSchurGenerated = require("./dependenciesSchur.generated.js");
var _dependenciesSubsetGenerated = require("./dependenciesSubset.generated.js");
var _dependenciesSubtractGenerated = require("./dependenciesSubtract.generated.js");
var _dependenciesTransposeGenerated = require("./dependenciesTranspose.generated.js");
var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");
var _factoriesAny = require("../../factoriesAny.js");
/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */

var sylvesterDependencies = exports.sylvesterDependencies = {
  absDependencies: _dependenciesAbsGenerated.absDependencies,
  addDependencies: _dependenciesAddGenerated.addDependencies,
  concatDependencies: _dependenciesConcatGenerated.concatDependencies,
  identityDependencies: _dependenciesIdentityGenerated.identityDependencies,
  indexDependencies: _dependenciesIndexGenerated.indexDependencies,
  lusolveDependencies: _dependenciesLusolveGenerated.lusolveDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  matrixFromColumnsDependencies: _dependenciesMatrixFromColumnsGenerated.matrixFromColumnsDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  rangeDependencies: _dependenciesRangeGenerated.rangeDependencies,
  schurDependencies: _dependenciesSchurGenerated.schurDependencies,
  subsetDependencies: _dependenciesSubsetGenerated.subsetDependencies,
  subtractDependencies: _dependenciesSubtractGenerated.subtractDependencies,
  transposeDependencies: _dependenciesTransposeGenerated.transposeDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createSylvester: _factoriesAny.createSylvester
};